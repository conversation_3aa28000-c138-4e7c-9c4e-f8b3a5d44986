from addict import Dict
from pathlib import Path


class Config:
    def __init__(self) -> None:
        super().__init__()
        self.common_config = Dict()
        self.common_config.patch_size = 128
        self.common_config.samples_per_slice = 20

        # 数据集相关
        self.IVOCT_Abbott = Dict()
        self.IVOCT_Abbott.dataset_name = "IVOCT_Abbott"  # 100 1025x497
        self.IVOCT_Abbott.dataset_path = Path("dataset/IVOCT/image/Abbott")
        # self.IVOCT_Abbott.train_list = list(range(0, 75))

        self.IVOCT_vivolight = Dict()
        self.IVOCT_vivolight.dataset_name = "IVOCT_vivolight"  # 100 1025x497
        self.IVOCT_vivolight.dataset_path = Path("dataset/IVOCT/image/vivolight")
        # self.IVOCT_vivolight.train_list = list(range(0, 75))

        self.AMD_SD = Dict()
        self.AMD_SD.dataset_name = "AMD-SD"  # 140 570x380
        self.AMD_SD.dataset_path = Path("dataset/AMD-SD/image")
        self.AMD_SD.train_list = list(range(0, 1))

        self.PKU37_noisy = Dict()
        self.PKU37_noisy.dataset_name = "PKU37_noisy"  # 27 640x640
        self.PKU37_noisy.dataset_path = Path("dataset/PKU37/noisy_paired")
        self.PKU37_noisy.train_list = list(range(0, 27))

        self.OCTA500_OCT_3mm = Dict()
        self.OCTA500_OCT_3mm.dataset_name = "OCTA500_OCT_3mm"  # 200 304x640
        self.OCTA500_OCT_3mm.dataset_path = Path("dataset/OCTA500/OCT/3mm")
        self.OCTA500_OCT_3mm.train_list = list(range(0, 185))

        self.OCTA500_OCT_6mm = Dict()
        self.OCTA500_OCT_6mm.dataset_name = "OCTA500_OCT_6mm"  # 300 400x640
        self.OCTA500_OCT_6mm.dataset_path = Path("dataset/OCTA500/OCT/6mm")
        # self.OCTA500_OCT_6mm.train_list = list(range(0, 300))

        self.OCTA500_OCTA_3mm = Dict()
        self.OCTA500_OCTA_3mm.dataset_name = "OCTA500_OCTA_3mm"  # 200 304x304
        self.OCTA500_OCTA_3mm.dataset_path = Path("dataset/OCTA500/OCTA/3mm/OCTA(OPL_BM)")
        # self.OCTA500_OCTA_3mm.train_list = list(range(0, 200))
        # self.OCTA500_OCTA_3mm.patch_size = 32

        self.OCTA500_OCTA_6mm = Dict()
        self.OCTA500_OCTA_6mm.dataset_name = "OCTA500_OCTA_6mm"  # 300 400x400
        self.OCTA500_OCTA_6mm.dataset_path = Path("dataset/OCTA500/OCTA/6mm/OCTA(OPL_BM)")
        # self.OCTA500_OCTA_6mm.train_list = list(range(0, 300))
        # self.OCTA500_OCTA_6mm.patch_size = 32

        self.ROSE1 = Dict()
        self.ROSE1.dataset_name = "ROSE1"  # 30 304x304
        self.ROSE1.dataset_path = Path("dataset/ROSE/ROSE-1/SVC_DVC/train/img")
        # self.ROSE1.train_list = list(range(0, 30))

        self.ROSE2 = Dict()
        self.ROSE2.dataset_name = "ROSE2"  # 90 512x512
        self.ROSE2.dataset_path = Path("dataset/ROSE/ROSE-2/train/original")
        # self.ROSE2.train_list = list(range(0, 90))

        self.PKU37_clean = Dict()
        self.PKU37_clean.dataset_name = "PKU37_clean"  # 33 640x640
        self.PKU37_clean.dataset_path = Path("dataset/PKU37/clean")
        # self.PKU37_clean.train_list = list(range(0, 33))
        self.PKU37_clean.patch_size = 128

        self.foci = Dict()
        self.foci.dataset_name = "foci"  # 1100x1800
        self.foci.dataset_path = Path("dataset/temp/foci")
        self.foci.train_list = list(range(0, 1))
        self.foci.patch_size = 64

        self.OCT3D = Dict()
        self.OCT3D.dataset_name = "OCT3D"
        self.OCT3D.dataset_path = Path("dataset/temp/OCT3D")
        self.OCT3D.train_list = list(range(0, 1))
        self.OCT3D.patch_size = 64

        self.OSE = Dict()
        self.OSE.dataset_name = "OSE"  # 1100x1800
        self.OSE.dataset_path = Path("dataset/temp/OSE")
        self.OSE.train_list = list(range(0, 1))
        self.OSE.patch_size = 128

        self.mapping = {
            1: self.IVOCT_Abbott,
            2: self.IVOCT_vivolight,
            3: self.AMD_SD,
            4: self.PKU37_noisy,
            5: self.OCTA500_OCT_3mm,
            6: self.OCTA500_OCT_6mm,
            7: self.OCTA500_OCTA_3mm,
            8: self.OCTA500_OCTA_6mm,
            9: self.ROSE1,
            10: self.ROSE2,
            11: self.PKU37_clean,
            15: self.foci,
            16: self.OCT3D,
            17: self.OSE,
        }

    def get_config(self, dataset_idx):
        d = self.common_config
        d.update(self.mapping[dataset_idx])
        return d
