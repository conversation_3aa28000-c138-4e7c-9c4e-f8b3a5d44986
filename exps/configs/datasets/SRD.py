from addict import Dict
from pathlib import Path


class Config:
    def __init__(self) -> None:
        super().__init__()
        self.common_config = Dict()
        self.common_config.samples_per_slice = 20

        # 数据集相关
        self.IVOCT_Abbott = Dict()
        self.IVOCT_Abbott.dataset_name = "IVOCT_<PERSON>"  # 100 1025x497
        self.IVOCT_Abbott.dataset_path = Path("dataset/IVOCT/image/Abbott")
        self.IVOCT_Abbott.train_list = list(range(0, 1))
        self.IVOCT_Abbott.patch_size = 128  # 128
        self.IVOCT_Abbott.samples_per_slice = 30
        self.IVOCT_Abbott.mean_std = [0.2846, 0.3891]
        self.IVOCT_Abbott.noise_factor = 12

        self.IVOCT_vivolight = Dict()
        self.IVOCT_vivolight.dataset_name = "IVOCT_vivolight"  # 220 1025x497
        self.IVOCT_vivolight.dataset_path = Path("dataset/IVOCT/image/vivolight")
        self.IVOCT_vivolight.train_list = list(range(0, 1))
        self.IVOCT_vivolight.patch_size = 64
        self.IVOCT_vivolight.mean_std = [0.4752, 0.4526]
        self.IVOCT_vivolight.noise_factor = 7

        self.PKU37_noisy = Dict()
        self.PKU37_noisy.dataset_name = "PKU37_noisy"  # 27 640x640
        self.PKU37_noisy.dataset_path = Path("dataset/PKU37/noisy_paired")
        self.PKU37_noisy.train_list = list(range(0, 1))
        self.PKU37_noisy.patch_size = 128
        self.PKU37_noisy.mean_std = [0.1003, 0.2870]
        self.PKU37_noisy.noise_factor = 3

        self.sparse_OCT = Dict()
        self.sparse_OCT.dataset_name = "sparse_OCT"
        self.sparse_OCT.dataset_path = Path("dataset/temp/Sparsity_SDOCT")
        self.sparse_OCT.train_list = list(range(0, 1))
        self.sparse_OCT.patch_size = 128
        self.sparse_OCT.noise_factor = 5

        self.foci = Dict()
        self.foci.dataset_name = "foci"  # 1100x1800
        self.foci.dataset_path = Path("dataset/temp/foci")
        self.foci.train_list = list(range(0, 1))
        self.foci.patch_size = 128
        self.foci.noise_factor = 1

        self.OCT3D = Dict()
        self.OCT3D.dataset_name = "OCT3D"
        self.OCT3D.dataset_path = Path("dataset/temp/OCT3D")
        self.OCT3D.train_list = list(range(0, 1))
        self.OCT3D.patch_size = 128
        self.OCT3D.noise_factor = 3

        self.OSE = Dict()
        self.OSE.dataset_name = "OSE"  # 1100x1800
        self.OSE.dataset_path = Path("dataset/temp/OSE")
        self.OSE.train_list = list(range(0, 1))
        self.OSE.patch_size = 128
        self.OSE.noise_factor = 2

        self.ABI = Dict()
        self.ABI.dataset_name = "ABI"  # 1100x1800
        self.ABI.dataset_path = Path("dataset/temp/ABI")
        self.ABI.train_list = list(range(0, 1))
        self.ABI.patch_size = 128
        self.ABI.noise_factor = 6

        self.shi_oct = Dict()
        self.shi_oct.dataset_name = "shi_oct"  # 1100x1800
        self.shi_oct.dataset_path = Path("dataset/temp/shi_oct")
        self.shi_oct.train_list = list(range(0, 1))
        self.shi_oct.patch_size = 128
        self.shi_oct.noise_factor = 2.5

        self.shi_asoct = Dict()
        self.shi_asoct.dataset_name = "shi_asoct"  # 1100x1800
        self.shi_asoct.dataset_path = Path("dataset/temp/shi_asoct")
        self.shi_asoct.train_list = list(range(0, 1))
        self.shi_asoct.patch_size = 128
        self.shi_asoct.noise_factor = 2.5

        self.mapping = {
            1: self.IVOCT_Abbott,
            2: self.IVOCT_vivolight,
            4: self.PKU37_noisy,
            12: self.sparse_OCT,
            15: self.foci,
            16: self.OCT3D,
            17: self.OSE,
            19: self.ABI,
            20: self.shi_oct,
            21: self.shi_asoct,
        }

    def get_config(self, dataset_idx):
        d = self.common_config
        d.update(self.mapping[dataset_idx])
        return d
