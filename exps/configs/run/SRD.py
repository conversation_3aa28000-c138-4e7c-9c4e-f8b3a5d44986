from addict import Dict
from pathlib import Path
from framework.config.basic import BasicConfig
from dataloader.datasets import Dataset_random_patch
from exps.configs.datasets.SRD import Config as DatasetConfig
from models.SRD.SRD import SRD

# 多子图

class Config(BasicConfig):
    def __init__(self) -> None:
        super().__init__()
        # 数据集相关
        self.data.dataset = Dataset_random_patch
        self.data.config = DatasetConfig()

        # 数据加载器相关
        self.dataloader.train.batch_size = 1
        self.dataloader.train.num_workers = 1
        self.dataloader.val.batch_size = 1
        self.dataloader.val.num_workers = 1
        self.dataloader.test.batch_size = 1
        self.dataloader.test.num_workers = 1
        # 模型相关
        self.model.net = SRD
        self.model.denoiser_in_ch = 3  # 3, 5, 2, 1
        self.model.out_ch = 1
        self.model.sr_num = 1  # 分辨率 * 2 ** sr_num
        self.model.base_ch = 4  # 16
        self.model.dt_rank = 32
        self.model.d_state = 32
        self.model.depth = 2
        self.model.vim_block_num = 2
        self.model.dropout = 0.1
        self.model.wavename = "haar"  # haar
        # 优化器相关
        self.optim = Dict()
        self.optim.lr = 1e-3  # 2e-4 for PKU37
        self.optim.weight_decay = 1e-4
        # 实验相关
        self.exp.script = "exps/scripts/SRD.py"
        self.exp.script_func = {  # func: subset
            # "train_denoiser": "train",
            # "infer_denoiser": "test",
            # "train_SR": "train",
            # "train_SR_single": "train",  # train, all
            "infer_SR_one_double": "test",  # test, all
        }
        self.exp.project_name = "SRD"  # 项目名称
        self.exp.exp_name = "SRD_test4"
        self.exp.train_dataset = 4
        self.exp.test_dataset = 4
        dataset_config = self.data.config.mapping[self.exp.train_dataset]
        trainset_num = len(dataset_config.train_list)
        self.exp.save_path = Path("output", self.exp.project_name, self.exp.exp_name, f'sample_{trainset_num}')
        self.exp.split_size = 32  # None or 16, eval、test、infer时的patch分组大小
        self.exp.compute_metrics = True
        self.exp.debug = False
        if self.exp.debug:
            self.exp.denoise_iters = 1
            self.exp.sr_iters = 1
            self.exp.val_freq = 1
            self.exp.gpu = "0"
        else:
            self.exp.denoise_iters = 50
            self.exp.sr_iters = 50
            self.exp.val_freq = 10
            self.exp.gpu = "1"
        self.exp.infer_denoiser_name = "minLoss"  # 推理的denoiser
        self.exp.infer_SR_name = "minLoss"  # SR推理的模型
        # self.exp.noise_factor = [0.1, 7, 7]  # 1,2
        # self.exp.noise_factor = [0.1, 4, 4]  # 4 0
        # self.exp.noise_factor = [0.1, 5, 5]  # 12
        # self.exp.noise_factor = [0.1, 1, 1]  # 15 0
        # self.exp.noise_factor = [0.1, 3, 3]  # 16  2
        # self.exp.noise_factor = [0.1, 2, 2]  # 17 0
        # self.exp.noise_factor = [0.1, 6, 6]  # 19

    def build(self):
        self.register_path([self.exp.save_path])
        super().build()


def get_config():
    """
    返回的配置必须是`framework.config.BasicConfig`的实例，函数名字必须为`get_config`。
    """
    return Config()
