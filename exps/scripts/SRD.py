import torch
import wandb
import random
import pandas as pd
from tqdm import tqdm
from copy import deepcopy
from torchvision import models
import torchvision.utils as tvu
from torch.nn import functional as F
from torch.utils.data import DataLoader
from torch_nlm import nlm2d

from models.utils_YSH import compute_metrics, get_rectangle_info_from_csv

torch.backends.cudnn.benchmark = True


class MeanShift(torch.nn.Conv2d):
    def __init__(self, data_mean, data_std, data_range=1, norm=True):
        c = len(data_mean)
        super(MeanShift, self).__init__(c, c, kernel_size=1)
        std = torch.Tensor(data_std)
        self.weight.data = torch.eye(c).view(c, c, 1, 1)
        if norm:
            self.weight.data.div_(std.view(c, 1, 1, 1))
            self.bias.data = -1 * data_range * torch.Tensor(data_mean)
            self.bias.data.div_(std)
        else:
            self.weight.data.mul_(std.view(c, 1, 1, 1))
            self.bias.data = data_range * torch.Tensor(data_mean)
        self.requires_grad = False


class VGGPerceptualLoss_full(torch.nn.Module):
    def __init__(self, mean_std=None):
        super(VGGPerceptualLoss_full, self).__init__()
        self.vgg_pretrained_features = models.vgg19(weights=models.VGG19_Weights.DEFAULT).features

        # 使用单通道的normalize
        if mean_std:
            data_mean = [mean_std[0]]
            data_std = [mean_std[1]]
        else:
            data_mean = [0.5]
            data_std = [0.229]
        self.normalize = MeanShift(data_mean, data_std, norm=True)
        # 将所有参数设置为不需要梯度
        for param in self.parameters():
            param.requires_grad = False

    def forward(self, X, Y, indices=None):
        # 先normalize
        X = self.normalize(X)
        Y = self.normalize(Y)

        # 重复3次以适配VGG
        X = X.repeat(1, 3, 1, 1)
        Y = Y.repeat(1, 3, 1, 1)

        indices = [2, 7, 12, 21, 30]
        weights = [1.0 / 2.6, 1.0 / 4.8, 1.0 / 3.7, 1.0 / 5.6, 10 / 1.5]
        k = 0
        loss = 0
        for i in range(indices[-1]):
            X = self.vgg_pretrained_features[i](X)
            Y = self.vgg_pretrained_features[i](Y)
            if (i + 1) in indices:
                loss += weights[k] * (X - Y.detach()).abs().mean() * 0.1
                k += 1
        return loss


class MS_SSIM(torch.nn.Module):
    """Multi-Scale Structural Similarity Index (MS-SSIM) Loss

    This implementation computes MS-SSIM loss across multiple scales to capture
    structural similarity at different resolutions. It's particularly effective
    for image super-resolution tasks.
    """

    def __init__(self, data_range=1.0, size_average=True, channel=1, weights=None):
        """
        Args:
            data_range (float): Value range of input images (usually 1.0 or 255)
            size_average (bool): If True, average the loss over batch dimension
            channel (int): Number of channels in input images
            weights (list): Weights for different scales. If None, uses default weights
        """
        super(MS_SSIM, self).__init__()
        self.data_range = data_range
        self.size_average = size_average
        self.channel = channel

        # Default weights for 5 scales as used in the original MS-SSIM paper
        if weights is None:
            self.weights = torch.tensor([0.0448, 0.2856, 0.3001, 0.2363, 0.1333])
        else:
            self.weights = torch.tensor(weights)

        # SSIM parameters
        self.K1 = 0.01
        self.K2 = 0.03
        self.C1 = (self.K1 * self.data_range) ** 2
        self.C2 = (self.K2 * self.data_range) ** 2

        # Gaussian kernel for SSIM computation
        self.kernel_size = 11
        self.sigma = 1.5
        self.gaussian_kernel = self._create_gaussian_kernel()

    def _create_gaussian_kernel(self):
        """Create 2D Gaussian kernel for SSIM computation"""
        coords = torch.arange(self.kernel_size, dtype=torch.float32)
        coords -= self.kernel_size // 2

        g = torch.exp(-(coords**2) / (2 * self.sigma**2))
        g /= g.sum()

        # Create 2D kernel
        kernel = g[:, None] * g[None, :]
        kernel = kernel.expand(self.channel, 1, self.kernel_size, self.kernel_size)

        return kernel

    def _ssim(self, X, Y):
        """Compute SSIM between two images"""
        # Ensure kernel is on the same device as input
        if self.gaussian_kernel.device != X.device:
            self.gaussian_kernel = self.gaussian_kernel.to(X.device)

        # Compute local means
        mu_X = F.conv2d(X, self.gaussian_kernel, padding=self.kernel_size // 2, groups=self.channel)
        mu_Y = F.conv2d(Y, self.gaussian_kernel, padding=self.kernel_size // 2, groups=self.channel)

        mu_X_sq = mu_X**2
        mu_Y_sq = mu_Y**2
        mu_XY = mu_X * mu_Y

        # Compute local variances and covariance
        sigma_X_sq = F.conv2d(X**2, self.gaussian_kernel, padding=self.kernel_size // 2, groups=self.channel) - mu_X_sq
        sigma_Y_sq = F.conv2d(Y**2, self.gaussian_kernel, padding=self.kernel_size // 2, groups=self.channel) - mu_Y_sq
        sigma_XY = F.conv2d(X * Y, self.gaussian_kernel, padding=self.kernel_size // 2, groups=self.channel) - mu_XY

        # Compute SSIM
        numerator = (2 * mu_XY + self.C1) * (2 * sigma_XY + self.C2)
        denominator = (mu_X_sq + mu_Y_sq + self.C1) * (sigma_X_sq + sigma_Y_sq + self.C2)

        ssim_map = numerator / (denominator + 1e-8)

        if self.size_average:
            return ssim_map.mean()
        else:
            return ssim_map.mean(dim=(1, 2, 3))

    def _ms_ssim(self, X, Y):
        """Compute Multi-Scale SSIM"""
        # Ensure weights are on the same device as input
        if self.weights.device != X.device:
            self.weights = self.weights.to(X.device)

        levels = len(self.weights)
        mcs = []  # Contrast and structure components

        for i in range(levels):
            if i > 0:
                # Downsample by factor of 2
                X = F.avg_pool2d(X, kernel_size=2, stride=2)
                Y = F.avg_pool2d(Y, kernel_size=2, stride=2)

            # Ensure minimum size
            if X.size(-1) < self.kernel_size or X.size(-2) < self.kernel_size:
                break

            ssim_val = self._ssim(X, Y)

            if i == levels - 1:
                # For the last scale, use the full SSIM
                mcs.append(ssim_val)
            else:
                # For other scales, extract contrast and structure components
                # This is a simplified version - in practice, you'd separate luminance, contrast, and structure
                mcs.append(ssim_val)

        # Compute weighted geometric mean
        ms_ssim = 1.0
        for i, mc in enumerate(mcs):
            if i < len(self.weights):
                ms_ssim *= mc ** self.weights[i]

        return ms_ssim

    def forward(self, X, Y):
        """
        Compute MS-SSIM loss

        Args:
            X (torch.Tensor): Predicted image [B, C, H, W]
            Y (torch.Tensor): Target image [B, C, H, W]

        Returns:
            torch.Tensor: MS-SSIM loss (1 - MS-SSIM for loss minimization)
        """
        # Ensure inputs are in the correct range
        X = torch.clamp(X, 0, self.data_range)
        Y = torch.clamp(Y, 0, self.data_range)

        ms_ssim_val = self._ms_ssim(X, Y)

        # Return 1 - MS-SSIM for loss minimization (higher MS-SSIM = lower loss)
        return 1.0 - ms_ssim_val


class TV_Loss(torch.nn.Module):
    def __init__(self):
        super(TV_Loss, self).__init__()

    def forward(self, x):
        """
        Args:
            x: 输入图像 [B, C, H, W]
        Returns:
            loss: TV损失
        """
        h_tv = torch.mean(torch.abs(x[:, :, 1:, :] - x[:, :, :-1, :]))
        w_tv = torch.mean(torch.abs(x[:, :, :, 1:] - x[:, :, :, :-1]))
        # h_tv = torch.mean(torch.pow(x[:, :, 1:, :] - x[:, :, :-1, :], 2))
        # w_tv = torch.mean(torch.pow(x[:, :, :, 1:] - x[:, :, :, :-1], 2))
        loss = h_tv + w_tv
        return loss


class Noise_simulator:
    def __init__(self, noise_factor=None):
        # if noise_factor:
        #     self.thresh, self.weight1, self.weight2 = noise_factor
        self.noise_factor = noise_factor

    def gaussian_noise(self, patch):
        """
        Args:
            patch: 输入图像 [B, C, H, W]
        """
        gaussian_noise = torch.randn_like(patch)
        patch_std = torch.std(patch, dim=(2, 3)).unsqueeze(1).unsqueeze(1)  # shape: [b, 1, 1, 1]
        # noise_factor = torch.where(
        #     patch_std < self.thresh,
        #     patch_std * self.weight1,
        #     self.weight1 * self.thresh + self.weight2 * (patch_std - self.thresh),
        # )
        noised_patch = patch + gaussian_noise * self.noise_factor * patch_std
        return noised_patch

    def gaussian_noise1(self, patch):
        """
        Args:
            patch: 输入图像 [B, C, H, W]
        """
        gaussian_noise = torch.randn_like(patch)
        patch_std = torch.std(patch, dim=(2, 3)).unsqueeze(1).unsqueeze(1)  # shape: [b, 1, 1, 1]
        patch_mean = torch.mean(patch, dim=(2, 3)).unsqueeze(1).unsqueeze(1)  # shape: [b, 1, 1, 1]
        # noise_factor = (1 - patch_mean) * self.weight1 + patch_std * self.weight2
        noise_factor = patch_std * patch_mean * 25.5
        # print(f"patch_mean: {patch_mean.mean()}, noise_factor: {noise_factor.mean()}")
        noised_patch = patch + gaussian_noise * noise_factor
        return noised_patch

    def filter_zero_pre(self, patch):
        '''
        检查patch中的0值，将0值替换为周围3x3区域的均值，非0值不作处理。如果patch中没有0值，则返回原始patch。
        Args:
            patch: 输入图像 [B, 1, H, W]
        Returns:
            filtered_patch: 处理后的图像 [B, 1, H, W]
        '''
        # 创建3x3卷积核用于计算局部均值
        kernel_size = 9
        padding = kernel_size // 2

        # 创建用于计算均值的卷积核 [1, 1, 3, 3]
        kernel = torch.ones(1, 1, kernel_size, kernel_size, device=patch.device) / (kernel_size * kernel_size)
        # 将kernel中心位置置为0，确保中心像素不影响平均效果
        center = kernel_size // 2
        kernel[0, 0, center, center] = 0
        kernel = kernel / kernel.sum()

        # 复制原始patch
        filtered_patch = patch.clone()

        # 找到零值位置
        patch_mean = torch.mean(patch, dim=(2, 3), keepdim=True)  # shape: [B, 1, 1, 1]
        # zero_mask = (patch <= 0.5 * patch_mean) & (patch >= 1.5 * patch_mean)  # 找到在阈值范围内的像素位置
        zero_mask = patch <= patch_mean  # 找到在阈值范围内的像素位置

        if zero_mask.any():
            # 使用卷积计算3x3区域的均值
            patch_padded = F.pad(patch, (padding, padding, padding, padding), mode='reflect')
            local_mean = F.conv2d(patch_padded, kernel)

            # 只在零值位置替换为局部均值
            filtered_patch = torch.where(zero_mask, local_mean, patch)
        tvu.save_image(patch, "original_patch.png", normalize=False)
        tvu.save_image(filtered_patch, "filtered_patch.png", normalize=False)
        return filtered_patch

    def filter(self, patch):
        '''
        1. 对patch做滑窗卷积，计算局部均值。
        '''
        kernel_size = 5
        padding = kernel_size // 2
        kernel = torch.ones(1, 1, kernel_size, kernel_size, device=patch.device) / (kernel_size * kernel_size)
        kernel = kernel / kernel.sum()
        filtered_patch = F.conv2d(patch, kernel, padding=padding)
        return filtered_patch
    
    def filter_nlm(self, patch):
        '''
        用NLM2D进行滤波，降噪保边缘
        支持批量处理 [B,C,H,W] 格式的输入
        '''
        original_shape = patch.shape
        B, C, H, W = original_shape
        
        # 重塑为 [B*C, H, W]，将每个通道作为独立的二维图像处理
        patch_reshaped = patch.view(B * C, H, W)
        
        # 批量应用nlm2d处理
        filtered_patches = []
        for i in range(B * C):
            # nlm2d期望输入为 [H, W]
            single_patch = patch_reshaped[i]
            filtered_single = nlm2d(single_patch, kernel_size=11, std=1.0, kernel_size_mean=3, sub_filter_size=16)
            filtered_patches.append(filtered_single)
        
        # 堆叠结果并重塑回原始形状
        filtered_tensor = torch.stack(filtered_patches, dim=0)  # [B*C, H, W]
        filtered_patch = filtered_tensor.view(B, C, H, W)  # 恢复原始形状
        
        return filtered_patch

    def filter_1(self, patch):
        '''
        1. 对patch做滑窗卷积，计算局部均值。
        2. 将patch中小于局部均值的像素替换为局部均值。
        '''
        # 创建3x3卷积核用于计算局部均值
        kernel_size = 5
        padding = kernel_size // 2
        kernel = torch.ones(1, 1, kernel_size, kernel_size, device=patch.device) / (kernel_size * kernel_size)
        # 将kernel中心位置置为0，确保中心像素不影响平均效果
        # center = kernel_size // 2
        # kernel[0, 0, center, center] = 0
        kernel = kernel / kernel.sum()
        # 复制原始patch
        filtered_patch = patch.clone()
        # 使用卷积计算3x3区域的均值
        patch_padded = F.pad(patch, (padding, padding, padding, padding), mode='reflect')
        local_mean = F.conv2d(patch_padded, kernel)
        # 只在小于局部均值的像素位置替换为局部均值
        filtered_patch = torch.where(filtered_patch < 0.1 * local_mean, local_mean, filtered_patch)

        kernel_size = 3
        padding = kernel_size // 2
        kernel = torch.ones(1, 1, kernel_size, kernel_size, device=patch.device) / (kernel_size * kernel_size)
        filtered_patch = F.conv2d(filtered_patch, kernel, padding=padding)
        return filtered_patch

    def filter_2(self, patch, kernel_size=9, epsilon=0.01):
        '''
        patch: [B, C, H, W]
        '''
        B, C, H, W = patch.shape
        pad = kernel_size // 2
        patch_padded = F.pad(patch, (pad, pad, pad, pad), mode='reflect')

        unfolded = F.unfold(patch_padded, kernel_size).view(B, C, kernel_size**2, H, W)
        local_mean = unfolded.mean(dim=2, keepdim=True)
        local_median = unfolded.median(dim=2, keepdim=True).values

        # 判断背景/前景
        is_background = local_mean > local_median + epsilon

        # 统一维度处理 - 去掉keepdim维度
        is_background = is_background.squeeze(2)  # [B, C, H, W]
        local_mean = local_mean.squeeze(2)  # [B, C, H, W]
        local_median = local_median.squeeze(2)  # [B, C, H, W]

        filtered_patch = torch.where((~is_background) & (patch < local_mean), local_median, patch)
        filtered_patch = torch.where(is_background & (patch > local_mean), local_median, filtered_patch)
        # 使用卷积核平滑处理
        kernel_size = 3
        padding = kernel_size // 2
        kernel = torch.ones(1, 1, kernel_size, kernel_size, device=patch.device) / (kernel_size * kernel_size)
        filtered_patch = F.conv2d(filtered_patch, kernel, padding=padding)

        return filtered_patch

    def filter_3(self, patch, kernel_size=9, epsilon=0.01, delta=0.005):
        '''
        改进的滤波函数，支持三类区域判别：前景、背景、过渡区域

        Args:
            patch: [B, C, H, W] 输入图像块
            kernel_size: 滑窗大小
            epsilon: 基础阈值
            delta: 附加阈值，用于扩大过渡区域

        Returns:
            filtered_patch: 滤波后的图像块
        '''
        B, C, H, W = patch.shape
        pad = kernel_size // 2
        patch_padded = F.pad(patch, (pad, pad, pad, pad), mode='reflect')

        unfolded = F.unfold(patch_padded, kernel_size).view(B, C, kernel_size**2, H, W)
        local_mean = unfolded.mean(dim=2, keepdim=True)
        local_median = unfolded.median(dim=2, keepdim=True).values

        diff = local_mean - local_median

        # 判别区域
        is_background = diff > (epsilon + delta)
        is_foreground = diff < -(epsilon + delta)
        is_transition = (~is_background) & (~is_foreground)

        # squeeze维度
        is_background = is_background.squeeze(2)
        is_foreground = is_foreground.squeeze(2)
        is_transition = is_transition.squeeze(2)
        local_mean = local_mean.squeeze(2)
        local_median = local_median.squeeze(2)

        filtered_patch = patch.clone()
        # 前景区域：去除异常低值
        filtered_patch = torch.where(is_background & (patch < local_mean), local_median, filtered_patch)
        # 背景区域：去除异常高值
        filtered_patch = torch.where(is_foreground & (patch > local_mean), local_median, filtered_patch)
        # 过渡区域：保留原值（这是保护弱信号的关键）

        # 平滑卷积（使用不同的变量名避免冲突）
        # smooth_kernel_size = 3
        # padding = smooth_kernel_size // 2
        # smooth_kernel = torch.ones(1, 1, smooth_kernel_size, smooth_kernel_size, device=patch.device) / (
        #     smooth_kernel_size * smooth_kernel_size
        # )
        # filtered_patch = F.conv2d(filtered_patch, smooth_kernel, padding=padding)

        return filtered_patch

    def filter_mad(self, patch, k=0.5):
        '''
        基于MAD的鲁棒异常检测，单向（极小）
        '''
        kernel_size = 5
        padding = kernel_size // 2

        # 提取邻域
        patches_unfolded = F.unfold(patch, kernel_size=kernel_size, padding=padding)
        b, c, h, w = patch.shape
        patches_reshaped = patches_unfolded.view(b, kernel_size * kernel_size, -1)

        # 计算邻域中位数
        median = patches_reshaped.median(dim=1, keepdim=True).values

        # 计算MAD
        abs_dev = torch.abs(patches_reshaped - median)
        mad = abs_dev.median(dim=1, keepdim=True).values + 1e-8  # 避免除0
        # 计算下界
        lower_bound = median - k * mad

        # 提取中心像素
        center_pixel = patch.view(b, c, -1)

        # 异常检测（单向）
        anomaly_mask = center_pixel < lower_bound

        # 额外条件：排除本身就是邻域无变异的点
        neighborhood_min = patches_reshaped.min(dim=1, keepdim=True).values
        neighborhood_max = patches_reshaped.max(dim=1, keepdim=True).values
        neighborhood_range = neighborhood_max - neighborhood_min
        anomaly_mask = anomaly_mask & (neighborhood_range > 0.05)  # 0.05是一个经验值，可以根据实际情况调整

        # 替换异常值为邻域中位数
        filtered_center = torch.where(anomaly_mask, median, center_pixel)

        # 重塑回原图
        filtered_patch = filtered_center.view(b, c, h, w)

        # 使用卷积核平滑处理
        # kernel_size = 3
        # padding = kernel_size // 2
        # kernel = torch.ones(1, 1, kernel_size, kernel_size, device=patch.device) / (kernel_size * kernel_size)
        # filtered_patch = F.conv2d(filtered_patch, kernel, padding=padding)

        return filtered_patch

    def adaptive_gaussian_noise(self, patch):
        """
        自适应高斯噪声，基于局部均值计算噪声权重
        Args:
            patch: 输入图像 [B, C, H, W]
        """
        # 创建滑窗卷积核来计算局部统计量
        kernel_size = 9
        padding = kernel_size // 2
        kernel = torch.ones(1, 1, kernel_size, kernel_size, device=patch.device) / (kernel_size * kernel_size)

        local_mean = F.conv2d(patch, kernel, padding=padding, groups=1)
        # print(torch.max(local_mean).item(), torch.min(local_mean).item(), torch.mean(local_mean).item())

        # 计算局部标准差
        local_var = F.conv2d((patch - local_mean) ** 2, kernel, padding=padding, groups=1)
        local_std = torch.sqrt(local_var + 1e-8)  # 添加小常数避免除零
        # tvu.save_image(local_std, "local_std.png", normalize=True)
        # tvu.save_image(local_mean, "local_mean.png", normalize=True)
        # tvu.save_image(patch, "patch.png", normalize=True)
        # 使用局部标准差作为噪声权重
        # noise_factor = (local_mean + local_std) * self.weight1
        noise_factor = local_mean * self.weight1 + local_std * self.weight2
        # noise_factor = local_std / local_mean
        # tvu.save_image(noise_factor, "noise_factor.png", normalize=True)
        gaussian_noise = torch.randn_like(patch)
        noised_patch = patch + gaussian_noise * 1# noise_factor
        # noised_patch = torch.clamp(noised_patch, 0, 1)  # 限制在[0, 1]范围内

        return noised_patch

    def adaptive_speckle_noise(self, patch):
        """
        自适应乘性噪声，基于局部均值计算噪声权重
        Args:
            patch: 输入图像 [B, C, H, W]
        """
        # 创建滑窗卷积核来计算局部统计量
        kernel_size = random.choice([3, 5, 7, 9])
        padding = kernel_size // 2
        kernel = torch.ones(1, 1, kernel_size, kernel_size, device=patch.device) / (kernel_size * kernel_size)
        gaussian_noise = torch.randn_like(patch)
        cor_noise = F.conv2d(gaussian_noise, kernel, padding=padding)
        local_mean = F.conv2d(patch, kernel, padding=padding, groups=1)
        local_var = F.conv2d((patch - local_mean) ** 2, kernel, padding=padding, groups=1)
        local_std = torch.sqrt(local_var + 1e-8)
        local_cv = local_std / (local_mean + 1e-8)  # 局部变异系数
        # noise_factor = local_cv * self.weight1 + local_std * self.weight2
        noised_patch = patch + cor_noise * local_cv * self.weight1
        # tvu.save_image(patch, "patch.png", normalize=True)
        # tvu.save_image(noised_patch, "noised_patch.png", normalize=True)
        noised_patch = torch.clamp(noised_patch, 0, 1)  # 限制在[0, 1]范围内
        return noised_patch


class Runner:
    def __init__(self, config):
        self.config = config
        self.datasets_config = config.data.config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.proj_dir = config.exp.save_path
        self.model_save_dir = self.proj_dir / "model"
        self.model_save_dir.mkdir(parents=True, exist_ok=True)
        self.infer_save_dir = self.proj_dir / "infer"
        self.infer_save_dir.mkdir(parents=True, exist_ok=True)
        self.L1_loss = torch.nn.L1Loss()
        self.L2_loss = torch.nn.MSELoss()

    def train_denoiser(self, subset="train"):
        self.wandb = wandb.init(
            project=self.config.exp.project_name,
            entity="YangBing_Team",
            config=self.config,
            name=self.config.exp.exp_name,
            dir=self.proj_dir,
        )

        model_save_dir = self.model_save_dir / "Denoiser"
        model_save_dir.mkdir(parents=True, exist_ok=True)

        # 创建单个数据集的dataloader
        dataset_config = self.datasets_config.get_config(self.config.exp.train_dataset)
        dataset = self.config.data.dataset(dataset_config, subset=subset)
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.train.batch_size,
            num_workers=self.config.dataloader.train.num_workers,
            pin_memory=True,
            persistent_workers=True,
            drop_last=True,
        )

        # 初始化模型和优化器
        model = self.config.model.net(self.config, in_ch=self.config.model.denoiser_in_ch).to(self.device)
        optimizer = torch.optim.Adam(
            model.parameters(),
            lr=self.config.optim.lr,
            weight_decay=self.config.optim.weight_decay,
        )
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=self.config.exp.denoise_iters)

        # 训练循环
        train_loss = 0
        minLoss = float("inf")

        # 创建数据迭代器
        data_iterator = iter(dataloader)
        noise_simulator = Noise_simulator(dataset_config.noise_factor)
        model.train()

        for iteration in tqdm(
            range(1, self.config.exp.denoise_iters + 1), total=self.config.exp.denoise_iters, desc="Train Denoiser"
        ):

            # 获取一个batch
            try:
                patches, _, _, _ = next(data_iterator)
            except StopIteration:
                data_iterator = iter(dataloader)
                patches, _, _, _ = next(data_iterator)

            # 将数据移到GPU
            patches = patches.to(self.device)
            # 训练步骤
            for i in range(patches.shape[1]):  # patches: [b, n_patches, 1, patch_size, patch_size]
                patch = patches[:, i, :, :, :]  # shape: [b, 1, patch_size, patch_size]
                filtered_patch = noise_simulator.filter(patch)
                # filtered_patch = patch
                input = []
                for _ in range(self.config.model.denoiser_in_ch):
                    noised_patch = noise_simulator.gaussian_noise(filtered_patch)
                    input.append(noised_patch)
                input = torch.cat(input, dim=1)

                optimizer.zero_grad()
                output = model(input)
                loss = self.L1_loss(output, filtered_patch)
                train_loss += loss.item()
                loss.backward()
                optimizer.step()

            scheduler.step()

            # 记录和保存
            if iteration % self.config.exp.val_freq == 0:
                avg_loss = train_loss / (self.config.exp.val_freq * patches.shape[1])
                train_loss = 0
                self.wandb.log({"train loss": avg_loss}, step=iteration)

                if iteration % self.config.exp.val_freq == 0:
                    model.eval()
                    vis_images, mean_score, val_loss = self.evaluate_denoiser(model)

                    # 创建一个包含所有数据集可视化结果的字典
                    vis_dict = {}
                    for img, dataset_name, image_stem in vis_images:
                        vis_dict[f"Denoised-{dataset_name}-{image_stem}"] = wandb.Image(img)
                        break

                    # 添加评估指标
                    vis_dict["scores"] = mean_score
                    vis_dict["val_loss"] = val_loss

                    self.wandb.log(vis_dict, step=iteration)
                    # 保存最over_denoised_score最好的模型
                    if val_loss < minLoss:
                        minLoss = val_loss
                        model_state = model.state_dict()
                        torch.save(model_state, model_save_dir / "minLoss.pth")

                    model.train()
        model_state = model.state_dict()
        torch.save(model_state, model_save_dir / "final.pth")

        self.wandb.finish()

    def evaluate_denoiser(self, model):
        model.eval()
        dataset_config = self.datasets_config.get_config(self.config.exp.train_dataset)
        dataset = self.config.data.dataset(dataset_config, subset="val")
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.val.batch_size,
            shuffle=False,
            num_workers=self.config.dataloader.val.num_workers,
            pin_memory=True,
            persistent_workers=True,
        )
        scores = []
        losses = 0.0
        total_samples = 0
        vis_images = []
        noise_simulator = Noise_simulator(dataset_config.noise_factor)

        for patches, indices, image_name, image_size in dataloader:
            image_name = image_name[0]
            outputs_list = []
            patches = torch.squeeze(patches, 0).to(self.device)  # [sample_num, 1, patch_size, patch_size]

            if self.config.exp.split_size:
                patches_batches = torch.split(patches, self.config.exp.split_size, dim=0)
            else:
                patches_batches = [patches]

            for patch in patches_batches:
                filtered_patch = noise_simulator.filter(patch)
                # filtered_patch = patch
                noised_input = []
                for _ in range(self.config.model.denoiser_in_ch):
                    noised_patch = noise_simulator.gaussian_noise(filtered_patch)
                    noised_input.append(noised_patch)
                noised_input = torch.cat(noised_input, dim=1)  # shape: [b, in_ch, patch_size, patch_size]
                origin_input = filtered_patch.repeat(1, self.config.model.denoiser_in_ch, 1, 1)
                with torch.no_grad():
                    noised_output = model(noised_input)  # shape: [b, 1, patch_size, patch_size]
                    original_output = model(origin_input)
                l1_loss = self.L1_loss(noised_output, filtered_patch)
                loss = l1_loss  # + vgg_loss
                losses += loss.item()
                noised_output = noised_output.detach().cpu()  # shape: [b, 1, patch_size, patch_size]
                original_output = original_output.detach().cpu()
                outputs_list.append(original_output)
                total_samples += 1
                if self.config.exp.debug:
                    break

            # 可视化处理
            image_size = [image_size[0].item(), image_size[1].item()]
            indices = indices.squeeze(0)
            outputs = torch.cat(outputs_list, dim=0)  # [samples_per_slice, 1, patch_size, patch_size]
            output_restored = dataset.patch_manager.restore_image(outputs, indices, image_size)

            # 保存图片
            vis_images.append((output_restored, dataset_config.dataset_name, image_name))

            # 只有当配置要求计算指标时才计算指标
            if self.config.exp.compute_metrics:
                patches = patches.detach().cpu()
                input_restored = dataset.patch_manager.restore_image(patches, indices, image_size)
                csv_path = dataset_config.dataset_path.joinpath(dataset_config.dataset_name + ".csv")
                rectangle_info = get_rectangle_info_from_csv(csv_path, image_name)  # 获取矩形信息
                score = compute_metrics(output_restored, input_restored, rectangle_info)
                scores.append(score)

            if self.config.exp.debug:
                break

        mean_score = {}
        if self.config.exp.compute_metrics and scores:
            for key in scores[0].keys():
                mean_score[key] = sum(score[key] for score in scores) / len(scores)
        model.train()
        return vis_images, mean_score, losses / (max(1, total_samples))

    def infer_denoiser(self, subset="test"):
        # 设置保存目录
        train_dataset = self.config.exp.train_dataset
        test_dataset = self.config.exp.test_dataset
        dir_name = f"{train_dataset}-->{test_dataset}"
        save_dir = self.infer_save_dir / "Denoiser" / dir_name / subset
        save_dir.mkdir(parents=True, exist_ok=True)

        # 加载模型
        model = self.config.model.net(self.config, in_ch=self.config.model.denoiser_in_ch).to(self.device)
        model_path = self.model_save_dir / "Denoiser" / f"{self.config.exp.infer_denoiser_name}.pth"
        model.load_state_dict(torch.load(model_path, weights_only=True), strict=True)
        model.eval()

        # 创建测试数据集的dataloader
        dataset_config = self.datasets_config.get_config(self.config.exp.test_dataset)
        dataset = self.config.data.dataset(dataset_config, subset=subset)
        dataset_name = dataset_config.dataset_name
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.test.batch_size,
            shuffle=False,
            num_workers=self.config.dataloader.test.num_workers,
            pin_memory=True,
        )
        noise_simulator = Noise_simulator(dataset_config.noise_factor)

        # 只在需要计算指标时创建结果DataFrame
        if self.config.exp.compute_metrics:
            results = pd.DataFrame()
            scores_list = []
            csv_path = dataset_config.dataset_path.joinpath(dataset_name + ".csv")

        # 处理每个样本
        for patches, indices, image_name, image_size in tqdm(dataloader, desc=f"Inferring {dataset_name}"):
            image_name = image_name[0]
            patches = torch.squeeze(patches, 0).to(self.device)  # [sample_num, 1, patch_size, patch_size]

            outputs_list = []
            filtered_paches_list = []

            # 将patches分成小批次进行处理
            if self.config.exp.split_size:
                patches_batches = torch.split(patches, self.config.exp.split_size, dim=0)
            else:
                patches_batches = [patches]
            for patch in patches_batches:
                filtered_patch = noise_simulator.filter(patch)
                # filtered_patch = patch
                model_input = filtered_patch.repeat(1, self.config.model.denoiser_in_ch, 1, 1)
                # model_input = patch.repeat(1, self.config.model.denoiser_in_ch, 1, 1)
                # model_input = []
                # tvu.save_image(patch, "original_patch.png", normalize=False)
                # tvu.save_image(filtered_patch, "filtered_patch.png", normalize=False)
                # for _ in range(self.config.model.denoiser_in_ch):
                #     model_input.append(filtered_patch)
                # model_input = torch.cat(model_input, dim=1)
                model_input = model_input.to(self.device)  # shape: [b, in_ch, patch_size, patch_size]
                with torch.no_grad():
                    output = model(model_input)

                output = output.detach().cpu()
                outputs_list.append(output)
                filtered_patch = filtered_patch.detach().cpu()  # shape: [b, 1, patch_size, patch_size]
                filtered_paches_list.append(filtered_patch)

            outputs = torch.cat(outputs_list, dim=0)  # [samples_per_slice, 1, patch_size, patch_size]
            filtered_patches = torch.cat(filtered_paches_list, dim=0)

            # 可视化处理
            image_size = [image_size[0].item(), image_size[1].item()]
            indices = indices.squeeze(0)
            patches = patches.detach().cpu()  # shape: [samples_per_slice, 1, patch_size, patch_size]
            output_restored = dataset.patch_manager.restore_image(outputs, indices, image_size)
            # 保存图片
            tvu.save_image(output_restored, save_dir / image_name, normalize=True)
            filtered_patches_restored = dataset.patch_manager.restore_image(filtered_patches, indices, image_size)
            tvu.save_image(filtered_patches_restored, save_dir / f"{image_name}_filtered.png", normalize=True)

            # 只有当配置要求计算指标时才计算指标
            if self.config.exp.compute_metrics:
                input_restored = dataset.patch_manager.restore_image(patches, indices, image_size)
                rectangle_info = get_rectangle_info_from_csv(csv_path, image_name)  # 获取矩形信息
                scores = compute_metrics(output_restored, input_restored, rectangle_info)
                scores_list.append(scores)

                # 动态构建结果行
                result_row = {"image_name": image_name}
                result_row.update(scores)
                results = pd.concat([results, pd.DataFrame([result_row])], ignore_index=True)

            if self.config.exp.debug:
                break

        # 只有当配置要求计算指标时才计算平均分数并保存结果
        if self.config.exp.compute_metrics:
            # 计算所有scores的平均值
            mean_score = {}
            if scores_list:
                for metric in scores_list[0].keys():
                    mean_score[metric] = sum(score[metric] for score in scores_list) / len(scores_list)

            # 添加均值行
            mean_row = {"image_name": "Average"}
            mean_row.update(mean_score)
            results = pd.concat([results, pd.DataFrame([mean_row])], ignore_index=True)

            # 保存结果
            results.to_csv(save_dir / "results.csv", index=False)

        print(f"推理完成，结果保存至 {save_dir}")

    def train_SR(self, subset="train"):
        self.wandb = wandb.init(
            project=self.config.exp.project_name,
            entity="YangBing_Team",
            config=self.config,
            name=self.config.exp.exp_name,
            dir=self.proj_dir,
        )
        model_save_dir = self.model_save_dir / f"SR_{self.config.model.sr_num}"
        model_save_dir.mkdir(parents=True, exist_ok=True)

        dataset_config = self.datasets_config.get_config(self.config.exp.train_dataset)
        dataset = self.config.data.dataset(dataset_config, subset=subset)
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.train.batch_size,
            num_workers=self.config.dataloader.train.num_workers,
            pin_memory=True,
            persistent_workers=True,
            drop_last=True,
        )
        # mean_std = dataset_config.get("mean_std", None)
        # vgg_loss = VGGPerceptualLoss_full().to(self.device)
        ms_ssim_loss = MS_SSIM(data_range=1.0, size_average=True, channel=1).to(self.device)
        # tv_loss = TV_Loss().to(self.device)
        # non_local_tv_loss = Non_Local_TV_Loss().to(self.device)

        # 初始化模型和优化器
        model = self.config.model.net(self.config, in_ch=1, sr=True).to(self.device)
        optimizer = torch.optim.Adam(
            model.parameters(),
            lr=self.config.optim.lr,
            weight_decay=self.config.optim.weight_decay,
        )
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=self.config.exp.sr_iters)

        # 加载预训练的denoiser到对应的GPU
        denoiser = self.config.model.net(self.config, in_ch=self.config.model.denoiser_in_ch, sr=False).to(self.device)
        denoiser_state = torch.load(
            self.model_save_dir / f"Denoiser/{self.config.exp.infer_denoiser_name}.pth",
            map_location="cpu",
            weights_only=True,
        )
        denoiser.load_state_dict(denoiser_state, strict=True)
        denoiser.eval()
        noise_simulator = Noise_simulator(dataset_config.noise_factor)

        # 训练循环
        minLoss = float("inf")
        train_loss = 0
        resize_params_down = ["area", "bilinear", "bicubic", "nearest"]

        # 创建数据迭代器
        data_iterator = iter(dataloader)
        model.train()

        for iteration in tqdm(range(1, self.config.exp.sr_iters + 1), total=self.config.exp.sr_iters, desc="Train SR"):

            # 获取一个batch
            try:
                patches, _, _, _ = next(data_iterator)
            except StopIteration:
                data_iterator = iter(dataloader)
                patches, _, _, _ = next(data_iterator)

            patches = patches.to(self.device)

            # 训练步骤
            for i in range(patches.shape[1]):  # patches: [b, n_patches, 1, patch_size, patch_size]
                patch = patches[:, i, :, :, :]  # shape: [b, 1, patch_size, patch_size]
                filtered_patch = noise_simulator.filter(patch)
                denoiser_input = filtered_patch.repeat(1, self.config.model.denoiser_in_ch, 1, 1)
                # denoiser_input = []
                # for _ in range(self.config.model.denoiser_in_ch):
                #     denoiser_input.append(filtered_patch)
                # denoiser_input = torch.cat(denoiser_input, dim=1)

                with torch.no_grad():
                    output_denoiser = denoiser(denoiser_input)

                small_patch = patch
                for sr_idx in range(self.config.model.sr_num):
                    resize_mode = random.choice(resize_params_down)
                    small_patch = F.interpolate(
                        small_patch,
                        scale_factor=0.5,
                        mode=resize_mode,
                        align_corners=True if resize_mode in ["bilinear", "bicubic"] else None,
                    )

                # 前向传播和反向传播
                optimizer.zero_grad()
                output_sr = model(small_patch)

                # 计算损失
                # l1loss = self.L1_loss(output_sr, nonzero_patch)
                ms_ssim_loss_val = ms_ssim_loss(output_sr, output_denoiser)
                # vggloss = vgg_loss(output_sr, nonzero_patch)
                loss = ms_ssim_loss_val  # + tv_loss(output_sr)
                train_loss += loss.item()
                loss.backward()
                optimizer.step()
                if self.config.exp.debug:
                    break

            # 每个iteration结束后更新一次学习率
            scheduler.step()

            # 记录和保存
            if iteration % self.config.exp.val_freq == 0:
                avg_loss = train_loss / (self.config.exp.val_freq * patches.shape[1])
                train_loss = 0
                self.wandb.log({"train loss": avg_loss}, step=iteration)

                if avg_loss < minLoss:
                    minLoss = avg_loss
                    model_state = model.state_dict()
                    torch.save(model_state, model_save_dir / "minLoss.pth")

                if iteration % self.config.exp.val_freq == 0:
                    model.eval()
                    val_loss = self.evaluate_SR(model, denoiser)
                    vis_double, sr_score = self.test_SR_one_double(model)

                    # 创建一个包含可视化结果的字典
                    vis_dict = {}
                    for img, dataset_name, image_name in vis_double:
                        vis_dict[f"SR-{dataset_name}-{image_name}"] = wandb.Image(img)
                        break

                    # 添加评估指标
                    vis_dict["sr_score"] = sr_score
                    vis_dict["val_loss"] = val_loss

                    self.wandb.log(vis_dict, step=iteration)
                    model.train()

        model_state = model.state_dict()
        torch.save(model_state, model_save_dir / "final.pth")
        self.wandb.finish()

    def evaluate_SR(self, model, denoiser=None):
        model.eval()

        dataset_config = self.datasets_config.get_config(self.config.exp.train_dataset)
        dataset = self.config.data.dataset(dataset_config, subset="val")
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.val.batch_size,
            shuffle=False,
            num_workers=self.config.dataloader.val.num_workers,
            pin_memory=True,
            persistent_workers=True,
        )

        losses = 0.0
        total_samples = 0
        noise_simulator = Noise_simulator(dataset_config.noise_factor)

        for patches, indices, image_name, image_size in dataloader:
            patches = patches.squeeze(0)  # [samples_per_slice, channels, height, width]
            # 将patches分成小批次进行处理
            if self.config.exp.split_size:
                patches_batches = torch.split(patches, self.config.exp.split_size, dim=0)
            else:
                patches_batches = [patches]

            for patch in patches_batches:
                patch = patch.to(self.device)
                # 预处理：自适应噪声
                nonzero_patch = noise_simulator.filter(patch)
                denoiser_input = nonzero_patch.repeat(1, self.config.model.denoiser_in_ch, 1, 1)

                # 降采样生成低分辨率输入
                small_patch = patch
                for sr_idx in range(self.config.model.sr_num):
                    small_patch = F.interpolate(
                        small_patch,
                        scale_factor=0.5,
                        mode="bicubic",
                        align_corners=True,
                    )

                # 预测
                with torch.no_grad():
                    output_denoiser = denoiser(denoiser_input)
                    output_sr = model(small_patch)  # shape: [split_size, 1, patch_size, patch_size]

                l1_loss = self.L1_loss(output_sr, output_denoiser)
                losses += l1_loss.item()
                total_samples += 1

            if self.config.exp.debug:
                break

        val_loss = losses / max(1, total_samples)
        model.train()

        return val_loss

    def train_SR_single(self, subset="train"):
        self.wandb = wandb.init(
            project=self.config.exp.project_name,
            entity="YangBing_Team",
            config=self.config,
            name=self.config.exp.exp_name,
            dir=self.proj_dir,
        )
        model_save_dir = self.model_save_dir / f"SR_{self.config.model.sr_num}"
        model_save_dir.mkdir(parents=True, exist_ok=True)

        dataset_config = self.datasets_config.get_config(self.config.exp.train_dataset)
        dataset = self.config.data.dataset(dataset_config, subset=subset)
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.train.batch_size,
            num_workers=self.config.dataloader.train.num_workers,
            pin_memory=True,
            persistent_workers=True,
            drop_last=True,
        )
        # mean_std = dataset_config.get("mean_std", None)
        # vgg_loss = VGGPerceptualLoss_full().to(self.device)
        ms_ssim_loss = MS_SSIM(data_range=1.0, size_average=True, channel=1).to(self.device)
        tv_loss = TV_Loss().to(self.device)
        # non_local_tv_loss = Non_Local_TV_Loss().to(self.device)

        # 初始化模型和优化器
        model = self.config.model.net(self.config, in_ch=1, sr=True).to(self.device)
        optimizer = torch.optim.Adam(
            model.parameters(),
            lr=self.config.optim.lr,
            weight_decay=self.config.optim.weight_decay,
        )
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=self.config.exp.sr_iters)

        noise_simulator = Noise_simulator(dataset_config.noise_factor)

        # 训练循环
        minLoss = float("inf")
        train_loss = 0
        resize_params_down = ["area", "bilinear", "bicubic", "nearest"]

        # 创建数据迭代器
        data_iterator = iter(dataloader)
        model.train()

        for iteration in tqdm(range(1, self.config.exp.sr_iters + 1), total=self.config.exp.sr_iters, desc="Train SR"):

            # 获取一个batch
            try:
                patches, _, _, _ = next(data_iterator)
            except StopIteration:
                data_iterator = iter(dataloader)
                patches, _, _, _ = next(data_iterator)

            patches = patches.to(self.device)

            # 训练步骤
            for i in range(patches.shape[1]):  # patches: [b, n_patches, 1, patch_size, patch_size]
                patch = patches[:, i, :, :, :]  # shape: [b, 1, patch_size, patch_size]
                filtered_patch = noise_simulator.filter(patch)

                small_patch = patch
                for sr_idx in range(self.config.model.sr_num):
                    resize_mode = random.choice(resize_params_down)
                    small_patch = F.interpolate(
                        small_patch,
                        scale_factor=0.5,
                        mode=resize_mode,
                        align_corners=True if resize_mode in ["bilinear", "bicubic"] else None,
                    )

                # 前向传播和反向传播
                optimizer.zero_grad()
                output_sr = model(small_patch)

                # 计算损失
                l1loss = self.L1_loss(output_sr, filtered_patch)
                ms_ssim_loss_val = ms_ssim_loss(output_sr, filtered_patch)
                # vggloss = vgg_loss(output_sr, filtered_patch)
                loss = ms_ssim_loss_val + l1loss + tv_loss(output_sr)
                train_loss += loss.item()
                loss.backward()
                optimizer.step()
                if self.config.exp.debug:
                    break

            # 每个iteration结束后更新一次学习率
            scheduler.step()

            # 记录和保存
            if iteration % self.config.exp.val_freq == 0:
                avg_loss = train_loss / (self.config.exp.val_freq * patches.shape[1])
                train_loss = 0
                self.wandb.log({"train loss": avg_loss}, step=iteration)

                if avg_loss < minLoss:
                    minLoss = avg_loss
                    model_state = model.state_dict()
                    torch.save(model_state, model_save_dir / "minLoss.pth")

                if iteration % self.config.exp.val_freq == 0:
                    model.eval()
                    val_loss = self.evaluate_SR_single(model)
                    vis_double, sr_score = self.test_SR_one_double(model)

                    # 创建一个包含可视化结果的字典
                    vis_dict = {}
                    for img, dataset_name, image_name in vis_double:
                        vis_dict[f"SR-{dataset_name}-{image_name}"] = wandb.Image(img)
                        break

                    # 添加评估指标
                    vis_dict["sr_score"] = sr_score
                    vis_dict["val_loss"] = val_loss

                    self.wandb.log(vis_dict, step=iteration)
                    model.train()

        model_state = model.state_dict()
        torch.save(model_state, model_save_dir / "final.pth")
        self.wandb.finish()

    def evaluate_SR_single(self, model):
        model.eval()

        dataset_config = self.datasets_config.get_config(self.config.exp.train_dataset)
        dataset = self.config.data.dataset(dataset_config, subset="val")
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.val.batch_size,
            shuffle=False,
            num_workers=self.config.dataloader.val.num_workers,
            pin_memory=True,
            persistent_workers=True,
        )

        losses = 0.0
        total_samples = 0
        noise_simulator = Noise_simulator(dataset_config.noise_factor)

        for patches, indices, image_name, image_size in dataloader:
            patches = patches.squeeze(0)  # [samples_per_slice, channels, height, width]
            # 将patches分成小批次进行处理
            if self.config.exp.split_size:
                patches_batches = torch.split(patches, self.config.exp.split_size, dim=0)
            else:
                patches_batches = [patches]

            for patch in patches_batches:
                patch = patch.to(self.device)
                # 预处理：自适应噪声
                filtered_patch = noise_simulator.filter(patch)

                # 降采样生成低分辨率输入
                small_patch = patch
                for sr_idx in range(self.config.model.sr_num):
                    small_patch = F.interpolate(
                        small_patch,
                        scale_factor=0.5,
                        mode="bicubic",
                        align_corners=True,
                    )

                # 预测
                with torch.no_grad():
                    output_sr = model(small_patch)  # shape: [split_size, 1, patch_size, patch_size]

                l1_loss = self.L1_loss(output_sr, filtered_patch)
                losses += l1_loss.item()
                total_samples += 1

            if self.config.exp.debug:
                break

        val_loss = losses / max(1, total_samples)
        model.train()

        return val_loss

    def infer_SR_half_one(self, subset="test"):
        # 设置保存目录
        train_dataset = self.config.exp.train_dataset
        test_dataset = self.config.exp.test_dataset
        dir_name = f"{train_dataset}-->{test_dataset}"
        SR_ratio = "SR_" + str(self.config.model.sr_num)
        save_dir = self.infer_save_dir / SR_ratio / "half_one" / dir_name / subset
        save_dir.mkdir(parents=True, exist_ok=True)

        # 加载SR模型
        model = self.config.model.net(self.config, in_ch=1, sr=True).to(self.device)
        state = torch.load(
            self.model_save_dir / SR_ratio / f"{self.config.exp.infer_SR_name}.pth",
            map_location="cpu",
            weights_only=True,
        )
        model.load_state_dict(state, strict=True)
        model.eval()

        # 创建测试数据集的dataloader
        dataset_config = self.datasets_config.get_config(self.config.exp.test_dataset)
        dataset = self.config.data.dataset(dataset_config, subset=subset)
        dataset_name = dataset_config.dataset_name
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.test.batch_size,
            shuffle=False,
            num_workers=self.config.dataloader.test.num_workers,
            pin_memory=True,
        )

        # 只在需要计算指标时创建结果DataFrame
        if self.config.exp.compute_metrics:
            results = pd.DataFrame()
            scores_list = []
            csv_path = dataset_config.dataset_path.joinpath(dataset_name + ".csv")

        # 处理每个样本
        for patches, indices, image_name, image_size in tqdm(dataloader, desc=f"Inferring {dataset_name}"):
            image_name = image_name[0]
            patches = patches.squeeze(0).to(self.device)  # [samples_per_slice, channels, height, width]

            outputs_list = []

            # 将patches分成小批次进行处理
            if self.config.exp.split_size:
                patches_batches = torch.split(patches, self.config.exp.split_size, dim=0)
            else:
                patches_batches = [patches]
            for patch in patches_batches:
                # 降采样生成低分辨率输入
                small_patch = patch
                for sr_idx in range(self.config.model.sr_num):
                    small_patch = F.interpolate(
                        small_patch,
                        scale_factor=0.5,
                        mode="bicubic",
                        align_corners=True,
                    )

                # 超分辨率预测
                with torch.no_grad():
                    output = model(small_patch)  # shape: [split_size, 1, patch_size, patch_size]

                output = output.detach().cpu()
                outputs_list.append(output)

            # 重组所有处理后的patches
            outputs = torch.cat(outputs_list, dim=0)

            # 可视化处理
            image_size = [image_size[0].item(), image_size[1].item()]
            indices = indices.squeeze(0)
            output_restored = dataset.patch_manager.restore_image(outputs, indices, image_size)

            # 保存图片
            tvu.save_image(output_restored, save_dir / image_name, normalize=True)

            # 只有当配置要求计算指标时才计算指标
            if self.config.exp.compute_metrics:
                patches = patches.detach().cpu()
                input_restored = dataset.patch_manager.restore_image(patches, indices, image_size)
                rectangle_info = get_rectangle_info_from_csv(csv_path, image_name)  # 获取矩形信息
                scores = compute_metrics(output_restored, input_restored, rectangle_info)
                scores_list.append(scores)

                # 动态构建结果行
                result_row = {"image_name": image_name}
                result_row.update(scores)
                results = pd.concat([results, pd.DataFrame([result_row])], ignore_index=True)

            if self.config.exp.debug:
                break

        # 只有当配置要求计算指标时才计算平均分数并保存结果
        if self.config.exp.compute_metrics:
            # 计算所有scores的平均值
            mean_score = {}
            if scores_list:
                for metric in scores_list[0].keys():
                    mean_score[metric] = sum(score[metric] for score in scores_list) / len(scores_list)

            # 添加均值行
            mean_row = {"image_name": "Average"}
            mean_row.update(mean_score)
            results = pd.concat([results, pd.DataFrame([mean_row])], ignore_index=True)

            # 保存结果
            results.to_csv(save_dir / "results.csv", index=False)

        print(f"推理完成，结果保存至 {save_dir}")

    def test_SR_one_double(self, model):
        model.eval()

        # 创建单个验证数据集的dataloader
        dataset_config = self.datasets_config.get_config(self.config.exp.train_dataset)
        dataset_config_ = deepcopy(dataset_config)
        patch_size = dataset_config_.patch_size // (2**self.config.model.sr_num)  # 调整patch_size
        dataset_config_.patch_size = patch_size
        dataset = self.config.data.dataset(dataset_config_, subset="val")
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.val.batch_size,
            shuffle=False,
            num_workers=self.config.dataloader.val.num_workers,
            pin_memory=True,
            persistent_workers=True,
        )

        scores = []
        vis_images = []  # 用列表存储可视化结果
        # noise_simulator = Noise_simulator(self.datasets_config.noise_factor)

        # 评估数据集
        dataset_name = dataset_config.dataset_name
        csv_path = dataset_config.dataset_path.joinpath(dataset_name + ".csv")

        for patches, indices, image_name, image_size in dataloader:
            # 将数据解包
            patches = patches.squeeze(0).to(self.device)  # [samples_per_slice, channels, height, width]
            outputs_list = []

            if self.config.exp.split_size:
                patches_batches = torch.split(patches, self.config.exp.split_size, dim=0)
            else:
                patches_batches = [patches]

            for patch in patches_batches:
                # filtered_patch = noise_simulator.filter(patch)

                with torch.no_grad():
                    output = model(patch)  # shape: [split_size, 1, patch_size, patch_size]

                output = F.interpolate(
                    output, scale_factor=1 / 2**self.config.model.sr_num, mode="bicubic", align_corners=True
                )

                output = output.detach().cpu()
                outputs_list.append(output)

            # 重组所有处理后的patches
            output = torch.cat(outputs_list, dim=0)
            patches = patches.detach().cpu()

            # 可视化处理
            image_size = [image_size[0].item(), image_size[1].item()]
            indices = indices.squeeze(0)
            output_restored = dataset.patch_manager.restore_image(output, indices, image_size)
            # 保存可视化结果
            image_name = image_name[0]
            vis_images.append((output_restored, dataset_name, image_name))

            # 只有当配置要求计算指标时才计算指标
            if self.config.exp.compute_metrics:
                input_restored = dataset.patch_manager.restore_image(patches, indices, image_size)
                rectangle_info = get_rectangle_info_from_csv(csv_path, image_name)  # 获取矩形信息
                score = compute_metrics(output_restored, input_restored, rectangle_info)
                scores.append(score)

            if self.config.exp.debug:
                break

        # 计算所有指标的平均值
        mean_score = {}
        if self.config.exp.compute_metrics and scores:
            for metric in scores[0].keys():
                mean_score[metric] = sum(score[metric] for score in scores) / len(scores)
        model.train()

        return vis_images, mean_score

    def infer_SR_one_double(self, subset="test"):
        # 设置保存目录
        train_dataset = self.config.exp.train_dataset
        test_dataset = self.config.exp.test_dataset
        dir_name = f"{train_dataset}-->{test_dataset}"
        SR_ratio = "SR_" + str(self.config.model.sr_num)
        save_dir = self.infer_save_dir / SR_ratio / "one_double" / dir_name / subset
        save_dir.mkdir(parents=True, exist_ok=True)

        # 加载SR模型
        model = self.config.model.net(self.config, in_ch=1, sr=True).to(self.device)
        state = torch.load(
            self.model_save_dir / SR_ratio / f"{self.config.exp.infer_SR_name}.pth",
            map_location="cpu",
            weights_only=True,
        )
        model.load_state_dict(state, strict=True)
        model.eval()

        # noise_simulator = Noise_simulator(self.datasets_config.noise_factor)

        # 创建测试数据集的dataloader
        dataset_config = self.datasets_config.get_config(self.config.exp.test_dataset)
        dataset_config_ = deepcopy(dataset_config)
        patch_size = dataset_config_.patch_size // (2**self.config.model.sr_num)  # 调整patch_size
        dataset_config_.patch_size = patch_size
        dataset = self.config.data.dataset(dataset_config_, subset=subset)
        dataset_name = dataset_config.dataset_name
        dataloader = DataLoader(
            dataset,
            batch_size=self.config.dataloader.test.batch_size,
            shuffle=False,
            num_workers=self.config.dataloader.test.num_workers,
            pin_memory=True,
        )

        # 只在subset为test且需要计算指标时创建结果DataFrame
        if subset == "test" and self.config.exp.compute_metrics:
            results = pd.DataFrame()
            scores_list = []
            csv_path = dataset_config.dataset_path.joinpath(dataset_name + ".csv")

        # 处理每个样本
        for patches, indices, image_name, image_size in tqdm(dataloader, desc=f"Inferring {dataset_name}"):
            image_name = image_name[0]
            patches = patches.squeeze(0).to(self.device)  # [samples_per_slice, channels, height, width]

            outputs_list = []

            # 将patches分成小批次进行处理
            if self.config.exp.split_size:
                patches_batches = torch.split(patches, self.config.exp.split_size, dim=0)
            else:
                patches_batches = [patches]

            for patch in patches_batches:
                # filtered_patch = noise_simulator.filter(patch)
                with torch.no_grad():
                    output = model(patch)  # shape: [split_size, 1, patch_size, patch_size]

                output = F.interpolate(
                    output, scale_factor=1 / 2**self.config.model.sr_num, mode="bicubic", align_corners=True
                )

                output = output.detach().cpu()
                outputs_list.append(output)

            # 重组所有处理后的patches
            output = torch.cat(outputs_list, dim=0)  # [samples_per_slice, 1, patch_size, patch_size]
            patches = patches.detach().cpu()  # [samples_per_slice, 1, patch_size, patch_size]

            # 可视化处理
            image_size = [image_size[0].item(), image_size[1].item()]
            indices = indices.squeeze(0)
            output_restored = dataset.patch_manager.restore_image(output, indices, image_size)
            # output_restored = (output_restored - output_restored.min()) / (
            #     output_restored.max() - output_restored.min()
            # )

            # 保存图片
            tvu.save_image(output_restored, save_dir / image_name, normalize=True)

            # 只在subset为test且需要计算指标时计算指标
            if subset == "test" and self.config.exp.compute_metrics:
                rectangle_info = get_rectangle_info_from_csv(csv_path, image_name)  # 获取矩形信息
                input_restored = dataset.patch_manager.restore_image(patches, indices, image_size)
                scores = compute_metrics(output_restored, input_restored, rectangle_info)
                scores_list.append(scores)
                # 动态构建结果行
                result_row = {"image_name": image_name}
                result_row.update(scores)
                results = pd.concat([results, pd.DataFrame([result_row])], ignore_index=True)

            if self.config.exp.debug:
                break

        # 只在mode为test且需要计算指标时计算平均分数并保存结果
        if subset == "test" and self.config.exp.compute_metrics:
            # 计算所有scores的平均值
            mean_score = {}
            if scores_list:
                for metric in scores_list[0].keys():
                    mean_score[metric] = sum(score[metric] for score in scores_list) / len(scores_list)

            # 添加均值行
            mean_row = {"image_name": "Average"}
            mean_row.update(mean_score)
            results = pd.concat([results, pd.DataFrame([mean_row])], ignore_index=True)

            # 保存结果
            results.to_csv(save_dir / "results.csv", index=False)

        print(f"推理完成，结果保存至 {save_dir}")


def main(config):
    runner = Runner(config)
    for func in config.exp.script_func:
        if hasattr(runner, func):
            # 执行训练函数，传入config中字典的值
            getattr(runner, func)(config.exp.script_func[func])
        else:
            raise ValueError(f"Invalid script function: {func}")
