{"time":"2025-08-06T22:40:49.339974517+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-06T22:40:49.51316978+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-06T22:40:49.513360904+08:00","level":"INFO","msg":"stream: created new stream","id":"q5ph3bxx"}
{"time":"2025-08-06T22:40:49.513383575+08:00","level":"INFO","msg":"stream: started","id":"q5ph3bxx"}
{"time":"2025-08-06T22:40:49.513402333+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"q5ph3bxx"}
{"time":"2025-08-06T22:40:49.513493077+08:00","level":"INFO","msg":"handler: started","stream_id":"q5ph3bxx"}
{"time":"2025-08-06T22:40:49.513553994+08:00","level":"INFO","msg":"sender: started","stream_id":"q5ph3bxx"}
{"time":"2025-08-06T22:40:49.514269152+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-06T22:44:43.093701731+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-06T22:44:43.097762224+08:00","level":"INFO","msg":"stream: closing","id":"q5ph3bxx"}
{"time":"2025-08-06T22:44:43.097782322+08:00","level":"INFO","msg":"handler: closed","stream_id":"q5ph3bxx"}
{"time":"2025-08-06T22:44:43.097793246+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"q5ph3bxx"}
{"time":"2025-08-06T22:44:43.097850891+08:00","level":"INFO","msg":"sender: closed","stream_id":"q5ph3bxx"}
{"time":"2025-08-06T22:44:43.097894748+08:00","level":"INFO","msg":"stream: closed","id":"q5ph3bxx"}
