{"time":"2025-08-03T12:02:29.025481626+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T12:02:29.192422444+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T12:02:29.192568881+08:00","level":"INFO","msg":"stream: created new stream","id":"swcjig2v"}
{"time":"2025-08-03T12:02:29.192592992+08:00","level":"INFO","msg":"stream: started","id":"swcjig2v"}
{"time":"2025-08-03T12:02:29.192683381+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"swcjig2v"}
{"time":"2025-08-03T12:02:29.192817627+08:00","level":"INFO","msg":"handler: started","stream_id":"swcjig2v"}
{"time":"2025-08-03T12:02:29.19281588+08:00","level":"INFO","msg":"sender: started","stream_id":"swcjig2v"}
{"time":"2025-08-03T12:02:29.193633219+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T12:07:35.558083351+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T12:07:35.56223242+08:00","level":"INFO","msg":"stream: closing","id":"swcjig2v"}
{"time":"2025-08-03T12:07:35.562267683+08:00","level":"INFO","msg":"handler: closed","stream_id":"swcjig2v"}
{"time":"2025-08-03T12:07:35.562281818+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"swcjig2v"}
{"time":"2025-08-03T12:07:35.562292038+08:00","level":"INFO","msg":"sender: closed","stream_id":"swcjig2v"}
{"time":"2025-08-03T12:07:35.562429267+08:00","level":"INFO","msg":"stream: closed","id":"swcjig2v"}
