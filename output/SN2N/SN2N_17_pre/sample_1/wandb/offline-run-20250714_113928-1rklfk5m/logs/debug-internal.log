{"time":"2025-07-14T11:39:28.437406139+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_17/sample_1/wandb/offline-run-20250714_113928-1rklfk5m/logs/debug-core.log"}
{"time":"2025-07-14T11:39:28.548143898+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-14T11:39:28.548419603+08:00","level":"INFO","msg":"stream: created new stream","id":"1rklfk5m"}
{"time":"2025-07-14T11:39:28.548447121+08:00","level":"INFO","msg":"stream: started","id":"1rklfk5m"}
{"time":"2025-07-14T11:39:28.548539655+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"1rklfk5m"}
{"time":"2025-07-14T11:39:28.548604462+08:00","level":"INFO","msg":"sender: started","stream_id":"1rklfk5m"}
{"time":"2025-07-14T11:39:28.548659079+08:00","level":"INFO","msg":"handler: started","stream_id":"1rklfk5m"}
{"time":"2025-07-14T11:39:28.554862008+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-14T11:42:58.781359543+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-14T11:42:58.781535271+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-14T11:42:58.782914386+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-14T11:42:58.787463352+08:00","level":"INFO","msg":"stream: closing","id":"1rklfk5m"}
{"time":"2025-07-14T11:42:58.787483279+08:00","level":"INFO","msg":"handler: closed","stream_id":"1rklfk5m"}
{"time":"2025-07-14T11:42:58.787495705+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"1rklfk5m"}
{"time":"2025-07-14T11:42:58.787571196+08:00","level":"INFO","msg":"sender: closed","stream_id":"1rklfk5m"}
{"time":"2025-07-14T11:42:58.787649471+08:00","level":"INFO","msg":"stream: closed","id":"1rklfk5m"}
