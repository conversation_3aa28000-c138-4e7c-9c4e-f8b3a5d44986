{"time":"2025-07-14T10:39:54.521565443+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_16/sample_1/wandb/offline-run-20250714_103954-afvt62yv/logs/debug-core.log"}
{"time":"2025-07-14T10:39:54.63531238+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-14T10:39:54.635498128+08:00","level":"INFO","msg":"stream: created new stream","id":"afvt62yv"}
{"time":"2025-07-14T10:39:54.635544123+08:00","level":"INFO","msg":"stream: started","id":"afvt62yv"}
{"time":"2025-07-14T10:39:54.635619378+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"afvt62yv"}
{"time":"2025-07-14T10:39:54.635697305+08:00","level":"INFO","msg":"handler: started","stream_id":"afvt62yv"}
{"time":"2025-07-14T10:39:54.63576038+08:00","level":"INFO","msg":"sender: started","stream_id":"afvt62yv"}
{"time":"2025-07-14T10:39:54.642517333+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-14T10:40:55.27676469+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-14T10:40:55.276882377+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-14T10:40:55.278157903+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-14T10:40:55.285846424+08:00","level":"INFO","msg":"stream: closing","id":"afvt62yv"}
{"time":"2025-07-14T10:40:55.285875984+08:00","level":"INFO","msg":"handler: closed","stream_id":"afvt62yv"}
{"time":"2025-07-14T10:40:55.285902614+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"afvt62yv"}
{"time":"2025-07-14T10:40:55.285911146+08:00","level":"INFO","msg":"sender: closed","stream_id":"afvt62yv"}
{"time":"2025-07-14T10:40:55.286123657+08:00","level":"INFO","msg":"stream: closed","id":"afvt62yv"}
