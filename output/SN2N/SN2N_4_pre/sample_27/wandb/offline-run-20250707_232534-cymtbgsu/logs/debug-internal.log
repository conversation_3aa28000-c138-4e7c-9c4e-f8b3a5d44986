{"time":"2025-07-07T23:25:34.563808127+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_4/sample_27/wandb/offline-run-20250707_232534-cymtbgsu/logs/debug-core.log"}
{"time":"2025-07-07T23:25:34.676775741+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-07T23:25:34.676952716+08:00","level":"INFO","msg":"stream: created new stream","id":"cymtbgsu"}
{"time":"2025-07-07T23:25:34.676981975+08:00","level":"INFO","msg":"stream: started","id":"cymtbgsu"}
{"time":"2025-07-07T23:25:34.677072309+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"cymtbgsu"}
{"time":"2025-07-07T23:25:34.677137267+08:00","level":"INFO","msg":"sender: started","stream_id":"cymtbgsu"}
{"time":"2025-07-07T23:25:34.677187875+08:00","level":"INFO","msg":"handler: started","stream_id":"cymtbgsu"}
{"time":"2025-07-07T23:25:34.683291501+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-07T23:28:03.067857466+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-07T23:28:03.067968675+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-07T23:28:03.069038188+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-07T23:28:03.072995895+08:00","level":"INFO","msg":"stream: closing","id":"cymtbgsu"}
{"time":"2025-07-07T23:28:03.07302206+08:00","level":"INFO","msg":"handler: closed","stream_id":"cymtbgsu"}
{"time":"2025-07-07T23:28:03.073035149+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"cymtbgsu"}
{"time":"2025-07-07T23:28:03.073055289+08:00","level":"INFO","msg":"sender: closed","stream_id":"cymtbgsu"}
{"time":"2025-07-07T23:28:03.073150298+08:00","level":"INFO","msg":"stream: closed","id":"cymtbgsu"}
