{"time":"2025-07-07T23:23:39.232368837+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_4/sample_27/wandb/offline-run-20250707_232338-2p1jrzy2/logs/debug-core.log"}
{"time":"2025-07-07T23:23:39.347815509+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-07T23:23:39.348019705+08:00","level":"INFO","msg":"stream: created new stream","id":"2p1jrzy2"}
{"time":"2025-07-07T23:23:39.348051812+08:00","level":"INFO","msg":"stream: started","id":"2p1jrzy2"}
{"time":"2025-07-07T23:23:39.348164935+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"2p1jrzy2"}
{"time":"2025-07-07T23:23:39.348217151+08:00","level":"INFO","msg":"handler: started","stream_id":"2p1jrzy2"}
{"time":"2025-07-07T23:23:39.348304406+08:00","level":"INFO","msg":"sender: started","stream_id":"2p1jrzy2"}
{"time":"2025-07-07T23:23:39.356482783+08:00","level":"INFO","msg":"Starting system monitor"}
