{"os": "Linux-6.1.83-1.2.3-x86_64-with-glibc2.35", "python": "CPython 3.12.11", "startedAt": "2025-07-07T15:23:39.226442Z", "args": ["--config", "exps/configs/run/SN2N.py"], "program": "boot_strap.py", "codePath": "boot_strap.py", "root": "output/SN2N/SN2N_4/sample_27", "host": "instance", "executable": "/root/miniconda3/envs/py12/bin/python", "codePathLocal": "boot_strap.py", "cpu_count": 32, "cpu_count_logical": 32, "gpu": "NVIDIA GeForce RTX 3090", "gpu_count": 2, "disk": {"/": {"total": "75161927680", "used": "71623671808"}}, "memory": {"total": "103079215104"}, "cpu": {"count": 32, "countLogical": 32}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 3090", "memoryTotal": "25769803776", "cudaCores": 10496, "architecture": "Ampere", "uuid": "GPU-be534bdf-d11d-330a-9000-7bee9d822240"}, {"name": "NVIDIA GeForce RTX 3090", "memoryTotal": "25769803776", "cudaCores": 10496, "architecture": "Ampere", "uuid": "GPU-39605349-5efe-fcc9-ba34-e8b4d869455b"}], "cudaVersion": "12.8"}