{"time":"2025-07-07T23:32:00.733148094+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_4/sample_1/wandb/offline-run-20250707_233200-cwrkk2d7/logs/debug-core.log"}
{"time":"2025-07-07T23:32:00.846240772+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-07T23:32:00.846416686+08:00","level":"INFO","msg":"stream: created new stream","id":"cwrkk2d7"}
{"time":"2025-07-07T23:32:00.84644514+08:00","level":"INFO","msg":"stream: started","id":"cwrkk2d7"}
{"time":"2025-07-07T23:32:00.846514046+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"cwrkk2d7"}
{"time":"2025-07-07T23:32:00.846581238+08:00","level":"INFO","msg":"sender: started","stream_id":"cwrkk2d7"}
{"time":"2025-07-07T23:32:00.846669702+08:00","level":"INFO","msg":"handler: started","stream_id":"cwrkk2d7"}
{"time":"2025-07-07T23:32:00.85283167+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-07T23:35:29.059165474+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-07T23:35:29.059309403+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-07T23:35:29.060371467+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-07T23:35:29.064670144+08:00","level":"INFO","msg":"stream: closing","id":"cwrkk2d7"}
{"time":"2025-07-07T23:35:29.064690602+08:00","level":"INFO","msg":"handler: closed","stream_id":"cwrkk2d7"}
{"time":"2025-07-07T23:35:29.064701927+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"cwrkk2d7"}
{"time":"2025-07-07T23:35:29.064712209+08:00","level":"INFO","msg":"sender: closed","stream_id":"cwrkk2d7"}
{"time":"2025-07-07T23:35:29.06483354+08:00","level":"INFO","msg":"stream: closed","id":"cwrkk2d7"}
