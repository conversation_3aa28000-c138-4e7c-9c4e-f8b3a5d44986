2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_setup.py:_flush():81] Configure stats pid to 37317
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_setup.py:_flush():81] Loading settings from /root/.config/wandb/settings
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_setup.py:_flush():81] Loading settings from /root/exp/yb/SRD/wandb/settings
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_init.py:setup_run_log_directory():703] Logging user logs to output/SN2N/SN2N_4/sample_1/wandb/offline-run-20250707_233200-cwrkk2d7/logs/debug.log
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to output/SN2N/SN2N_4/sample_1/wandb/offline-run-20250707_233200-cwrkk2d7/logs/debug-internal.log
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_init.py:init():831] calling init triggers
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'data': {'dataset': <class 'dataloader.datasets.Dataset_random_patch'>, 'config': <exps.configs.datasets.SN2N.Config object at 0x7f5355dbf7a0>}, 'model': {'net': <class 'models.unets.UNet'>, 'img_ch': 1, 'output_ch': 1, 'channels': [64, 128, 256, 512, 1024], 'data_augmenter': <class 'models.Denoise_Nets.SN2N.DataAugmenter'>}, 'dataloader': {'train': {'batch_size': 1, 'num_workers': 1}, 'val': {'batch_size': 1, 'num_workers': 1}, 'test': {'batch_size': 1, 'num_workers': 1}}, 'exp': {'project_name': 'SN2N', 'exp_name': 'SN2N_4', 'gpu': '0', 'path_to_init': [PosixPath('output/SN2N/SN2N_4/sample_1')], 'script': 'exps.scripts.SN2N', 'script_func': {'train': 'train', 'infer': 'test'}, 'train_dataset': 4, 'test_dataset': 4, 'save_path': PosixPath('output/SN2N/SN2N_4/sample_1'), 'compute_metrics': True, 'debug': False, 'iters': 1000, 'snapshot_freq': 500, 'val_freq': 100, 'infer_model_name': 'minLoss'}, 'optim': {'lr': 0.0002, 'weight_decay': 0.0001}, '_wandb': {}}
2025-07-07 23:32:00,509 INFO    MainThread:37317 [wandb_init.py:init():872] starting backend
2025-07-07 23:32:00,720 INFO    MainThread:37317 [wandb_init.py:init():875] sending inform_init request
2025-07-07 23:32:00,727 INFO    MainThread:37317 [wandb_init.py:init():883] backend started and connected
2025-07-07 23:32:00,729 INFO    MainThread:37317 [wandb_init.py:init():956] updated telemetry
2025-07-07 23:32:00,730 INFO    MainThread:37317 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-07-07 23:32:00,849 INFO    MainThread:37317 [wandb_init.py:init():1032] starting run threads in backend
2025-07-07 23:32:00,948 INFO    MainThread:37317 [wandb_run.py:_console_start():2453] atexit reg
2025-07-07 23:32:00,948 INFO    MainThread:37317 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-07-07 23:32:00,949 INFO    MainThread:37317 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-07-07 23:32:00,949 INFO    MainThread:37317 [wandb_run.py:_redirect():2393] Redirects installed.
2025-07-07 23:32:00,951 INFO    MainThread:37317 [wandb_init.py:init():1078] run started, returning control to user process
2025-07-07 23:35:29,057 INFO    MainThread:37317 [wandb_run.py:_finish():2219] finishing run YangBing_Team/SN2N/cwrkk2d7
2025-07-07 23:35:29,058 INFO    MainThread:37317 [wandb_run.py:_atexit_cleanup():2418] got exitcode: 0
2025-07-07 23:35:29,058 INFO    MainThread:37317 [wandb_run.py:_restore():2400] restore
2025-07-07 23:35:29,058 INFO    MainThread:37317 [wandb_run.py:_restore():2406] restore done
2025-07-07 23:35:29,063 INFO    MainThread:37317 [wandb_run.py:_footer_history_summary_info():4000] rendering history
2025-07-07 23:35:29,063 INFO    MainThread:37317 [wandb_run.py:_footer_history_summary_info():4032] rendering summary
