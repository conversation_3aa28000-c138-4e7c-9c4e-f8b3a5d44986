{"time":"2025-08-06T22:17:18.782677263+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_4/sample_1/wandb/offline-run-20250806_221718-nz3e8zj7/logs/debug-core.log"}
{"time":"2025-08-06T22:17:18.895192651+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-06T22:17:18.895554866+08:00","level":"INFO","msg":"stream: created new stream","id":"nz3e8zj7"}
{"time":"2025-08-06T22:17:18.895583798+08:00","level":"INFO","msg":"stream: started","id":"nz3e8zj7"}
{"time":"2025-08-06T22:17:18.895665819+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"nz3e8zj7"}
{"time":"2025-08-06T22:17:18.895735019+08:00","level":"INFO","msg":"sender: started","stream_id":"nz3e8zj7"}
{"time":"2025-08-06T22:17:18.89581377+08:00","level":"INFO","msg":"handler: started","stream_id":"nz3e8zj7"}
{"time":"2025-08-06T22:17:18.902731636+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-06T22:22:07.498776643+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-06T22:22:07.498923104+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-06T22:22:07.50021332+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-06T22:22:07.504543539+08:00","level":"INFO","msg":"stream: closing","id":"nz3e8zj7"}
{"time":"2025-08-06T22:22:07.504566904+08:00","level":"INFO","msg":"handler: closed","stream_id":"nz3e8zj7"}
{"time":"2025-08-06T22:22:07.504584266+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"nz3e8zj7"}
{"time":"2025-08-06T22:22:07.504589581+08:00","level":"INFO","msg":"sender: closed","stream_id":"nz3e8zj7"}
{"time":"2025-08-06T22:22:07.504744537+08:00","level":"INFO","msg":"stream: closed","id":"nz3e8zj7"}
