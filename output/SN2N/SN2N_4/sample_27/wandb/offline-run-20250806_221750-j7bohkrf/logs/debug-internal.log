{"time":"2025-08-06T22:17:50.634044788+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-06T22:17:50.793270427+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-06T22:17:50.793493281+08:00","level":"INFO","msg":"stream: created new stream","id":"j7bohkrf"}
{"time":"2025-08-06T22:17:50.793534946+08:00","level":"INFO","msg":"stream: started","id":"j7bohkrf"}
{"time":"2025-08-06T22:17:50.793633619+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"j7bohkrf"}
{"time":"2025-08-06T22:17:50.793692017+08:00","level":"INFO","msg":"handler: started","stream_id":"j7bohkrf"}
{"time":"2025-08-06T22:17:50.793774035+08:00","level":"INFO","msg":"sender: started","stream_id":"j7bohkrf"}
{"time":"2025-08-06T22:17:50.794559422+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-06T22:18:01.814232532+08:00","level":"INFO","msg":"stream: closing","id":"j7bohkrf"}
{"time":"2025-08-06T22:18:01.814544232+08:00","level":"INFO","msg":"handler: closed","stream_id":"j7bohkrf"}
{"time":"2025-08-06T22:18:01.814576232+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"j7bohkrf"}
{"time":"2025-08-06T22:18:01.814616966+08:00","level":"INFO","msg":"sender: closed","stream_id":"j7bohkrf"}
{"time":"2025-08-06T22:18:01.814701173+08:00","level":"INFO","msg":"stream: closed","id":"j7bohkrf"}
