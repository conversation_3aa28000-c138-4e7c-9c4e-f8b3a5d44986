2025-08-06 22:18:14,969 INFO    MainThread:41183 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-06 22:18:14,969 INFO    MainThread:41183 [wandb_setup.py:_flush():80] Configure stats pid to 41183
2025-08-06 22:18:14,969 INFO    MainThread:41183 [wandb_setup.py:_flush():80] Loading settings from /root/.config/wandb/settings
2025-08-06 22:18:14,969 INFO    MainThread:41183 [wandb_setup.py:_flush():80] Loading settings from /root/exp/yb/SRD/wandb/settings
2025-08-06 22:18:14,970 INFO    MainThread:41183 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-06 22:18:14,970 INFO    MainThread:41183 [wandb_init.py:setup_run_log_directory():703] Logging user logs to output/SN2N/SN2N_4/sample_27/wandb/offline-run-20250806_221814-0h1yq1wf/logs/debug.log
2025-08-06 22:18:14,970 INFO    MainThread:41183 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to output/SN2N/SN2N_4/sample_27/wandb/offline-run-20250806_221814-0h1yq1wf/logs/debug-internal.log
2025-08-06 22:18:14,970 INFO    MainThread:41183 [wandb_init.py:init():830] calling init triggers
2025-08-06 22:18:14,970 INFO    MainThread:41183 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'data': {'dataset': <class 'dataloader.datasets.Dataset_random_patch'>, 'config': <exps.configs.datasets.SN2N.Config object at 0x7fc7634b9010>}, 'model': {'net': <class 'models.unets.UNet'>, 'img_ch': 1, 'output_ch': 1, 'channels': [64, 128, 256, 512, 1024], 'data_augmenter': <class 'models.Denoise_Nets.SN2N.DataAugmenter'>}, 'dataloader': {'train': {'batch_size': 4, 'num_workers': 1}, 'val': {'batch_size': 1, 'num_workers': 1}, 'test': {'batch_size': 1, 'num_workers': 1}}, 'exp': {'project_name': 'SN2N', 'exp_name': 'SN2N_4', 'gpu': '0', 'path_to_init': [PosixPath('output/SN2N/SN2N_4/sample_27')], 'script': 'exps.scripts.SN2N', 'script_func': {'train': 'train', 'infer': 'test'}, 'train_dataset': 4, 'test_dataset': 4, 'save_path': PosixPath('output/SN2N/SN2N_4/sample_27'), 'compute_metrics': False, 'debug': False, 'iters': 1000, 'snapshot_freq': 500, 'val_freq': 100, 'infer_model_name': 'minLoss'}, 'optim': {'lr': 0.0002, 'weight_decay': 0.0001}, '_wandb': {}}
2025-08-06 22:18:14,970 INFO    MainThread:41183 [wandb_init.py:init():871] starting backend
2025-08-06 22:18:15,177 INFO    MainThread:41183 [wandb_init.py:init():874] sending inform_init request
2025-08-06 22:18:15,182 INFO    MainThread:41183 [wandb_init.py:init():882] backend started and connected
2025-08-06 22:18:15,184 INFO    MainThread:41183 [wandb_init.py:init():953] updated telemetry
2025-08-06 22:18:15,185 INFO    MainThread:41183 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-06 22:18:15,354 INFO    MainThread:41183 [wandb_init.py:init():1029] starting run threads in backend
2025-08-06 22:18:15,457 INFO    MainThread:41183 [wandb_run.py:_console_start():2458] atexit reg
2025-08-06 22:18:15,458 INFO    MainThread:41183 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-06 22:18:15,458 INFO    MainThread:41183 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-06 22:18:15,458 INFO    MainThread:41183 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-06 22:18:15,460 INFO    MainThread:41183 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-06 22:29:12,980 INFO    MainThread:41183 [wandb_run.py:_finish():2224] finishing run YangBing_Team/SN2N/0h1yq1wf
2025-08-06 22:29:12,980 INFO    MainThread:41183 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-08-06 22:29:12,981 INFO    MainThread:41183 [wandb_run.py:_restore():2405] restore
2025-08-06 22:29:12,981 INFO    MainThread:41183 [wandb_run.py:_restore():2411] restore done
2025-08-06 22:29:12,986 INFO    MainThread:41183 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-08-06 22:29:12,986 INFO    MainThread:41183 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
