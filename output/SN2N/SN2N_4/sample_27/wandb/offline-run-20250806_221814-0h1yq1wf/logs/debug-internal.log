{"time":"2025-08-06T22:18:15.189683563+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-06T22:18:15.35061819+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-06T22:18:15.350821346+08:00","level":"INFO","msg":"stream: created new stream","id":"0h1yq1wf"}
{"time":"2025-08-06T22:18:15.350854051+08:00","level":"INFO","msg":"stream: started","id":"0h1yq1wf"}
{"time":"2025-08-06T22:18:15.35092816+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"0h1yq1wf"}
{"time":"2025-08-06T22:18:15.350967873+08:00","level":"INFO","msg":"handler: started","stream_id":"0h1yq1wf"}
{"time":"2025-08-06T22:18:15.351037061+08:00","level":"INFO","msg":"sender: started","stream_id":"0h1yq1wf"}
{"time":"2025-08-06T22:18:15.351891746+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-06T22:29:12.983063928+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-06T22:29:12.987539856+08:00","level":"INFO","msg":"stream: closing","id":"0h1yq1wf"}
{"time":"2025-08-06T22:29:12.987568971+08:00","level":"INFO","msg":"handler: closed","stream_id":"0h1yq1wf"}
{"time":"2025-08-06T22:29:12.987579944+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"0h1yq1wf"}
{"time":"2025-08-06T22:29:12.98765653+08:00","level":"INFO","msg":"sender: closed","stream_id":"0h1yq1wf"}
{"time":"2025-08-06T22:29:12.98773071+08:00","level":"INFO","msg":"stream: closed","id":"0h1yq1wf"}
