{"time":"2025-07-13T22:23:04.451323814+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_15/sample_1/wandb/offline-run-20250713_222304-1uqlilwe/logs/debug-core.log"}
{"time":"2025-07-13T22:23:04.564921288+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-13T22:23:04.565117303+08:00","level":"INFO","msg":"stream: created new stream","id":"1uqlilwe"}
{"time":"2025-07-13T22:23:04.565154998+08:00","level":"INFO","msg":"stream: started","id":"1uqlilwe"}
{"time":"2025-07-13T22:23:04.565203877+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"1uqlilwe"}
{"time":"2025-07-13T22:23:04.565234804+08:00","level":"INFO","msg":"sender: started","stream_id":"1uqlilwe"}
{"time":"2025-07-13T22:23:04.56527443+08:00","level":"INFO","msg":"handler: started","stream_id":"1uqlilwe"}
{"time":"2025-07-13T22:23:04.572423889+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-13T22:23:24.595859773+08:00","level":"INFO","msg":"stream: closing","id":"1uqlilwe"}
{"time":"2025-07-13T22:23:24.5959324+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-13T22:23:24.596032947+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-13T22:23:24.596265398+08:00","level":"INFO","msg":"handler: closed","stream_id":"1uqlilwe"}
{"time":"2025-07-13T22:23:24.596302158+08:00","level":"INFO","msg":"sender: closed","stream_id":"1uqlilwe"}
{"time":"2025-07-13T22:23:24.59630142+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"1uqlilwe"}
{"time":"2025-07-13T22:23:24.596439229+08:00","level":"INFO","msg":"stream: closed","id":"1uqlilwe"}
