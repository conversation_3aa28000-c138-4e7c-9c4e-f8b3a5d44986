{"time":"2025-07-13T22:23:33.96436107+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_15/sample_1/wandb/offline-run-20250713_222333-7ajtq16x/logs/debug-core.log"}
{"time":"2025-07-13T22:23:34.078853315+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-13T22:23:34.079040681+08:00","level":"INFO","msg":"stream: created new stream","id":"7ajtq16x"}
{"time":"2025-07-13T22:23:34.079073162+08:00","level":"INFO","msg":"stream: started","id":"7ajtq16x"}
{"time":"2025-07-13T22:23:34.07917123+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"7ajtq16x"}
{"time":"2025-07-13T22:23:34.079256734+08:00","level":"INFO","msg":"handler: started","stream_id":"7ajtq16x"}
{"time":"2025-07-13T22:23:34.079392131+08:00","level":"INFO","msg":"sender: started","stream_id":"7ajtq16x"}
{"time":"2025-07-13T22:23:34.08514049+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-13T22:24:38.641154058+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-13T22:24:38.641285229+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-13T22:24:38.64270602+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-13T22:24:38.647888987+08:00","level":"INFO","msg":"stream: closing","id":"7ajtq16x"}
{"time":"2025-07-13T22:24:38.647906585+08:00","level":"INFO","msg":"handler: closed","stream_id":"7ajtq16x"}
{"time":"2025-07-13T22:24:38.647918439+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"7ajtq16x"}
{"time":"2025-07-13T22:24:38.647931543+08:00","level":"INFO","msg":"sender: closed","stream_id":"7ajtq16x"}
{"time":"2025-07-13T22:24:38.647983577+08:00","level":"INFO","msg":"stream: closed","id":"7ajtq16x"}
