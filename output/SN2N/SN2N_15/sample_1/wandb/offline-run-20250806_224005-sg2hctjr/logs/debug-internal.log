{"time":"2025-08-06T22:40:05.561088009+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_15/sample_1/wandb/offline-run-20250806_224005-sg2hctjr/logs/debug-core.log"}
{"time":"2025-08-06T22:40:05.675687272+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-06T22:40:05.675869186+08:00","level":"INFO","msg":"stream: created new stream","id":"sg2hctjr"}
{"time":"2025-08-06T22:40:05.675902161+08:00","level":"INFO","msg":"stream: started","id":"sg2hctjr"}
{"time":"2025-08-06T22:40:05.675978288+08:00","level":"INFO","msg":"sender: started","stream_id":"sg2hctjr"}
{"time":"2025-08-06T22:40:05.67604461+08:00","level":"INFO","msg":"handler: started","stream_id":"sg2hctjr"}
{"time":"2025-08-06T22:40:05.675975855+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"sg2hctjr"}
{"time":"2025-08-06T22:40:05.68201733+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-06T22:40:25.704789094+08:00","level":"INFO","msg":"stream: closing","id":"sg2hctjr"}
{"time":"2025-08-06T22:40:25.704860308+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-06T22:40:25.704921631+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-06T22:40:25.70515683+08:00","level":"INFO","msg":"handler: closed","stream_id":"sg2hctjr"}
{"time":"2025-08-06T22:40:25.70519951+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"sg2hctjr"}
{"time":"2025-08-06T22:40:25.705215993+08:00","level":"INFO","msg":"sender: closed","stream_id":"sg2hctjr"}
{"time":"2025-08-06T22:40:25.705458069+08:00","level":"INFO","msg":"stream: closed","id":"sg2hctjr"}
