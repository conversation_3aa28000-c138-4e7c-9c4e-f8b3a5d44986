{"time":"2025-08-06T22:40:39.849662202+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SN2N/SN2N_16/sample_1/wandb/offline-run-20250806_224039-ycyhbb9n/logs/debug-core.log"}
{"time":"2025-08-06T22:40:39.960967199+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-06T22:40:39.96114559+08:00","level":"INFO","msg":"stream: created new stream","id":"ycyhbb9n"}
{"time":"2025-08-06T22:40:39.961177529+08:00","level":"INFO","msg":"stream: started","id":"ycyhbb9n"}
{"time":"2025-08-06T22:40:39.961277048+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ycyhbb9n"}
{"time":"2025-08-06T22:40:39.96134707+08:00","level":"INFO","msg":"sender: started","stream_id":"ycyhbb9n"}
{"time":"2025-08-06T22:40:39.961382037+08:00","level":"INFO","msg":"handler: started","stream_id":"ycyhbb9n"}
{"time":"2025-08-06T22:40:39.967714246+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-06T22:42:14.35254641+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-06T22:42:14.352640346+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-06T22:42:14.353804397+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-06T22:42:14.359652052+08:00","level":"INFO","msg":"stream: closing","id":"ycyhbb9n"}
{"time":"2025-08-06T22:42:14.359670629+08:00","level":"INFO","msg":"handler: closed","stream_id":"ycyhbb9n"}
{"time":"2025-08-06T22:42:14.359682759+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"ycyhbb9n"}
{"time":"2025-08-06T22:42:14.359689815+08:00","level":"INFO","msg":"sender: closed","stream_id":"ycyhbb9n"}
{"time":"2025-08-06T22:42:14.359774702+08:00","level":"INFO","msg":"stream: closed","id":"ycyhbb9n"}
