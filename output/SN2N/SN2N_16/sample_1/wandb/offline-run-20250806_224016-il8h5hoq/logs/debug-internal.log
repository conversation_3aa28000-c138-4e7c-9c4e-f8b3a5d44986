{"time":"2025-08-06T22:40:16.414738918+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-06T22:40:16.576971846+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-06T22:40:16.577170057+08:00","level":"INFO","msg":"stream: created new stream","id":"il8h5hoq"}
{"time":"2025-08-06T22:40:16.577207834+08:00","level":"INFO","msg":"stream: started","id":"il8h5hoq"}
{"time":"2025-08-06T22:40:16.577291087+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"il8h5hoq"}
{"time":"2025-08-06T22:40:16.577362404+08:00","level":"INFO","msg":"handler: started","stream_id":"il8h5hoq"}
{"time":"2025-08-06T22:40:16.577422011+08:00","level":"INFO","msg":"sender: started","stream_id":"il8h5hoq"}
{"time":"2025-08-06T22:40:16.578466419+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-06T22:40:35.60624894+08:00","level":"INFO","msg":"stream: closing","id":"il8h5hoq"}
{"time":"2025-08-06T22:40:35.606617269+08:00","level":"INFO","msg":"handler: closed","stream_id":"il8h5hoq"}
{"time":"2025-08-06T22:40:35.606651673+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"il8h5hoq"}
{"time":"2025-08-06T22:40:35.60666806+08:00","level":"INFO","msg":"sender: closed","stream_id":"il8h5hoq"}
{"time":"2025-08-06T22:40:35.606908561+08:00","level":"INFO","msg":"stream: closed","id":"il8h5hoq"}
