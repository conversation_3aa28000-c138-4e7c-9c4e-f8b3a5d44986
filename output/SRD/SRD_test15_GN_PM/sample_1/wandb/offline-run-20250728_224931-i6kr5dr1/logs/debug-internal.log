{"time":"2025-07-28T22:49:31.184537267+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test15_GN_PM/sample_1/wandb/offline-run-20250728_224931-i6kr5dr1/logs/debug-core.log"}
{"time":"2025-07-28T22:49:31.294826305+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:49:31.294938141+08:00","level":"INFO","msg":"stream: created new stream","id":"i6kr5dr1"}
{"time":"2025-07-28T22:49:31.294959844+08:00","level":"INFO","msg":"stream: started","id":"i6kr5dr1"}
{"time":"2025-07-28T22:49:31.295006125+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"i6kr5dr1"}
{"time":"2025-07-28T22:49:31.295086739+08:00","level":"INFO","msg":"sender: started","stream_id":"i6kr5dr1"}
{"time":"2025-07-28T22:49:31.295052874+08:00","level":"INFO","msg":"handler: started","stream_id":"i6kr5dr1"}
{"time":"2025-07-28T22:49:31.299942388+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:55:07.35681974+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:55:07.356934442+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:55:07.357834593+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T22:55:07.361125522+08:00","level":"INFO","msg":"stream: closing","id":"i6kr5dr1"}
{"time":"2025-07-28T22:55:07.361154463+08:00","level":"INFO","msg":"handler: closed","stream_id":"i6kr5dr1"}
{"time":"2025-07-28T22:55:07.361169476+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"i6kr5dr1"}
{"time":"2025-07-28T22:55:07.361174921+08:00","level":"INFO","msg":"sender: closed","stream_id":"i6kr5dr1"}
{"time":"2025-07-28T22:55:07.361284488+08:00","level":"INFO","msg":"stream: closed","id":"i6kr5dr1"}
