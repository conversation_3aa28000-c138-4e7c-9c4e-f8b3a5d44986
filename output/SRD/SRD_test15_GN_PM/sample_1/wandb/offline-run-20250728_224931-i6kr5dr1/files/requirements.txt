setuptools==78.1.1
wheel==0.45.1
pip==25.1
click==8.1.8
Brotli==1.0.9
cycler==0.11.0
kiwisolver==1.4.8
mkl-service==2.4.0
packaging==24.2
pillow==11.1.0
pyparsing==3.2.0
tzdata==2025.2
pytz==2024.1
six==1.17.0
unicodedata2==15.1.0
fonttools==4.55.3
numpy==2.0.1
python-dateutil==2.9.0.post0
Bottleneck==1.4.2
contourpy==1.3.1
matplotlib==3.10.0
mkl_fft==1.3.11
mkl_random==1.2.8
numexpr==2.10.1
seaborn==0.13.2
opencv-python==*********
tqdm==4.67.1
joblib==1.4.2
threadpoolctl==3.5.0
scipy==1.15.3
scikit-learn==1.6.1
networkx==3.4.2
lazy_loader==0.4
imageio==2.37.0
tifffile==2025.2.18
scikit-image==0.25.0
wcwidth==0.2.13
nvidia-ml-py==12.575.51
psutil==7.0.0
blessed==1.21.0
gpustat==1.1.1
einops==0.8.1
urllib3==2.4.0
typing_extensions==4.14.0
smmap==5.0.2
setproctitle==1.3.6
PyYAML==6.0.2
protobuf==5.29.5
platformdirs==4.3.8
idna==3.10
charset-normalizer==3.4.2
certifi==2025.4.26
annotated-types==0.7.0
typing-inspection==0.4.1
sentry-sdk==2.29.1
requests==2.32.4
pydantic_core==2.33.2
gitdb==4.0.12
pydantic==2.11.5
GitPython==3.1.44
wandb==0.20.1
addict==2.4.0
nvidia-cusparselt-cu12==0.6.3
mpmath==1.3.0
triton==3.3.1
sympy==1.14.0
nvidia-nvtx-cu12==12.8.55
nvidia-nvjitlink-cu12==12.8.61
nvidia-nccl-cu12==2.26.2
nvidia-curand-cu12==*********
nvidia-cufile-cu12==*********
nvidia-cuda-runtime-cu12==12.8.57
nvidia-cuda-nvrtc-cu12==12.8.61
nvidia-cuda-cupti-cu12==12.8.57
nvidia-cublas-cu12==*********
MarkupSafe==3.0.2
fsspec==2025.5.1
filelock==3.18.0
nvidia-cusparse-cu12==12.5.7.53
nvidia-cufft-cu12==11.3.3.41
nvidia-cudnn-cu12==9.7.1.26
Jinja2==3.1.6
nvidia-cusolver-cu12==11.7.2.55
torch==2.7.1+cu128
torchvision==0.22.1+cu128
torchaudio==2.7.1+cu128
pytorch-wavelets==1.3.0
PyWavelets==1.8.0
pydicom==3.0.1
wrapt==1.17.2
swankit==0.2.4
simplejson==3.20.1
pynvml==12.0.0
Pygments==2.19.2
prettytable==3.16.0
mdurl==0.1.2
jmespath==1.0.1
pyecharts==2.0.8
markdown-it-py==3.0.0
botocore==1.39.14
s3transfer==0.13.1
rich==13.9.4
boto3==1.39.14
swanlab==0.6.7
soxr==0.5.0.post1
pycparser==2.22
msgpack==1.1.1
llvmlite==0.44.0
decorator==5.2.1
audioread==3.0.1
pooch==1.8.2
numba==0.61.2
cffi==1.17.1
soundfile==0.13.1
librosa==0.11.0
Werkzeug==3.1.3
tensorboard-data-server==0.7.2
Markdown==3.8.2
grpcio==1.74.0
absl-py==2.3.1
pydub==0.25.1
pandas==2.3.1
namex==0.1.0
libclang==18.1.1
flatbuffers==25.2.10
termcolor==3.1.0
optree==0.17.0
opt_einsum==3.4.0
ml_dtypes==0.5.1
h5py==3.14.0
google-pasta==0.2.0
gast==0.6.0
astunparse==1.6.3
tensorboard==2.19.0
keras==3.10.0
tensorflow==2.19.0
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.2
platformdirs==4.2.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.45.1
zipp==3.19.2
