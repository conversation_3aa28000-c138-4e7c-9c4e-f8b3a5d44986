{"time":"2025-07-28T21:48:56.355801269+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test15_GN_PM/sample_1/wandb/offline-run-20250728_214856-gh5w3z3g/logs/debug-core.log"}
{"time":"2025-07-28T21:48:56.465781861+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T21:48:56.465899665+08:00","level":"INFO","msg":"stream: created new stream","id":"gh5w3z3g"}
{"time":"2025-07-28T21:48:56.465919824+08:00","level":"INFO","msg":"stream: started","id":"gh5w3z3g"}
{"time":"2025-07-28T21:48:56.466002362+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"gh5w3z3g"}
{"time":"2025-07-28T21:48:56.46607749+08:00","level":"INFO","msg":"handler: started","stream_id":"gh5w3z3g"}
{"time":"2025-07-28T21:48:56.466134029+08:00","level":"INFO","msg":"sender: started","stream_id":"gh5w3z3g"}
{"time":"2025-07-28T21:48:56.471087307+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T21:54:47.550530546+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T21:54:47.550638887+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T21:54:47.551380628+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T21:54:47.554181525+08:00","level":"INFO","msg":"stream: closing","id":"gh5w3z3g"}
{"time":"2025-07-28T21:54:47.5542119+08:00","level":"INFO","msg":"handler: closed","stream_id":"gh5w3z3g"}
{"time":"2025-07-28T21:54:47.554225563+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"gh5w3z3g"}
{"time":"2025-07-28T21:54:47.554241911+08:00","level":"INFO","msg":"sender: closed","stream_id":"gh5w3z3g"}
{"time":"2025-07-28T21:54:47.554303207+08:00","level":"INFO","msg":"stream: closed","id":"gh5w3z3g"}
