{"time":"2025-07-28T22:46:02.283044721+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test15_GN_PM/sample_1/wandb/offline-run-20250728_224602-zbfoza11/logs/debug-core.log"}
{"time":"2025-07-28T22:46:02.393937679+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:46:02.394086218+08:00","level":"INFO","msg":"stream: created new stream","id":"zbfoza11"}
{"time":"2025-07-28T22:46:02.394134544+08:00","level":"INFO","msg":"stream: started","id":"zbfoza11"}
{"time":"2025-07-28T22:46:02.394211122+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"zbfoza11"}
{"time":"2025-07-28T22:46:02.394280198+08:00","level":"INFO","msg":"sender: started","stream_id":"zbfoza11"}
{"time":"2025-07-28T22:46:02.394329447+08:00","level":"INFO","msg":"handler: started","stream_id":"zbfoza11"}
{"time":"2025-07-28T22:46:02.400549105+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:49:28.394764492+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:49:28.394866912+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:49:28.395937795+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T22:49:28.400070699+08:00","level":"INFO","msg":"stream: closing","id":"zbfoza11"}
{"time":"2025-07-28T22:49:28.400121325+08:00","level":"INFO","msg":"handler: closed","stream_id":"zbfoza11"}
{"time":"2025-07-28T22:49:28.400139613+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"zbfoza11"}
{"time":"2025-07-28T22:49:28.400151343+08:00","level":"INFO","msg":"sender: closed","stream_id":"zbfoza11"}
{"time":"2025-07-28T22:49:28.400222982+08:00","level":"INFO","msg":"stream: closed","id":"zbfoza11"}
