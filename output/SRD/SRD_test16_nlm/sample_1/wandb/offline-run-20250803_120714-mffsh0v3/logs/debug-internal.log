{"time":"2025-08-03T12:07:14.215803107+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test16_nlm/sample_1/wandb/offline-run-20250803_120714-mffsh0v3/logs/debug-core.log"}
{"time":"2025-08-03T12:07:14.326980744+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T12:07:14.327170005+08:00","level":"INFO","msg":"stream: created new stream","id":"mffsh0v3"}
{"time":"2025-08-03T12:07:14.327202091+08:00","level":"INFO","msg":"stream: started","id":"mffsh0v3"}
{"time":"2025-08-03T12:07:14.327285901+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"mffsh0v3"}
{"time":"2025-08-03T12:07:14.327328041+08:00","level":"INFO","msg":"sender: started","stream_id":"mffsh0v3"}
{"time":"2025-08-03T12:07:14.327376907+08:00","level":"INFO","msg":"handler: started","stream_id":"mffsh0v3"}
{"time":"2025-08-03T12:07:14.330888571+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-03T12:13:13.723211152+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-03T12:13:13.72331124+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-03T12:13:13.7242105+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T12:13:13.727648886+08:00","level":"INFO","msg":"stream: closing","id":"mffsh0v3"}
{"time":"2025-08-03T12:13:13.727675317+08:00","level":"INFO","msg":"handler: closed","stream_id":"mffsh0v3"}
{"time":"2025-08-03T12:13:13.727689573+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"mffsh0v3"}
{"time":"2025-08-03T12:13:13.727736983+08:00","level":"INFO","msg":"sender: closed","stream_id":"mffsh0v3"}
{"time":"2025-08-03T12:13:13.727794491+08:00","level":"INFO","msg":"stream: closed","id":"mffsh0v3"}
