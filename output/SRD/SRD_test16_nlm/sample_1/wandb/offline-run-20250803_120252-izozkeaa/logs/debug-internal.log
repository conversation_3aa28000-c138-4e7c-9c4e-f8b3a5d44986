{"time":"2025-08-03T12:02:52.838220139+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test16_nlm/sample_1/wandb/offline-run-20250803_120252-izozkeaa/logs/debug-core.log"}
{"time":"2025-08-03T12:02:52.947992415+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T12:02:52.9481506+08:00","level":"INFO","msg":"stream: created new stream","id":"izozkeaa"}
{"time":"2025-08-03T12:02:52.948191287+08:00","level":"INFO","msg":"stream: started","id":"izozkeaa"}
{"time":"2025-08-03T12:02:52.948263273+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"izozkeaa"}
{"time":"2025-08-03T12:02:52.948333903+08:00","level":"INFO","msg":"handler: started","stream_id":"izozkeaa"}
{"time":"2025-08-03T12:02:52.948386366+08:00","level":"INFO","msg":"sender: started","stream_id":"izozkeaa"}
{"time":"2025-08-03T12:02:52.954483132+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-03T12:07:10.697528993+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-03T12:07:10.697642761+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-03T12:07:10.698614819+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T12:07:10.702390861+08:00","level":"INFO","msg":"stream: closing","id":"izozkeaa"}
{"time":"2025-08-03T12:07:10.702413074+08:00","level":"INFO","msg":"handler: closed","stream_id":"izozkeaa"}
{"time":"2025-08-03T12:07:10.702423286+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"izozkeaa"}
{"time":"2025-08-03T12:07:10.702486778+08:00","level":"INFO","msg":"sender: closed","stream_id":"izozkeaa"}
{"time":"2025-08-03T12:07:10.702504772+08:00","level":"INFO","msg":"stream: closed","id":"izozkeaa"}
