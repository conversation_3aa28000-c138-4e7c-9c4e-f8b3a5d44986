{"time":"2025-08-03T15:17:17.108035182+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T15:17:17.263899469+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T15:17:17.264045723+08:00","level":"INFO","msg":"stream: created new stream","id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:17.264068432+08:00","level":"INFO","msg":"stream: started","id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:17.26415761+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:17.264190216+08:00","level":"INFO","msg":"handler: started","stream_id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:17.264246879+08:00","level":"INFO","msg":"sender: started","stream_id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:17.264878767+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T15:17:23.277698396+08:00","level":"INFO","msg":"stream: closing","id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:23.277960431+08:00","level":"INFO","msg":"handler: closed","stream_id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:23.277973858+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:23.277991955+08:00","level":"INFO","msg":"sender: closed","stream_id":"iwe3tvzo"}
{"time":"2025-08-03T15:17:23.278061407+08:00","level":"INFO","msg":"stream: closed","id":"iwe3tvzo"}
