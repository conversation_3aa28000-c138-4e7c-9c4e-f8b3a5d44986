{"time":"2025-08-03T15:17:50.278733437+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T15:17:50.438697665+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T15:17:50.4388313+08:00","level":"INFO","msg":"stream: created new stream","id":"a51etvmd"}
{"time":"2025-08-03T15:17:50.438854466+08:00","level":"INFO","msg":"stream: started","id":"a51etvmd"}
{"time":"2025-08-03T15:17:50.438947397+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"a51etvmd"}
{"time":"2025-08-03T15:17:50.439006175+08:00","level":"INFO","msg":"sender: started","stream_id":"a51etvmd"}
{"time":"2025-08-03T15:17:50.439066744+08:00","level":"INFO","msg":"handler: started","stream_id":"a51etvmd"}
{"time":"2025-08-03T15:17:50.439887974+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T15:21:55.875984294+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T15:21:55.880614063+08:00","level":"INFO","msg":"stream: closing","id":"a51etvmd"}
{"time":"2025-08-03T15:21:55.880651398+08:00","level":"INFO","msg":"handler: closed","stream_id":"a51etvmd"}
{"time":"2025-08-03T15:21:55.880664604+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"a51etvmd"}
{"time":"2025-08-03T15:21:55.880706265+08:00","level":"INFO","msg":"sender: closed","stream_id":"a51etvmd"}
{"time":"2025-08-03T15:21:55.880753324+08:00","level":"INFO","msg":"stream: closed","id":"a51etvmd"}
