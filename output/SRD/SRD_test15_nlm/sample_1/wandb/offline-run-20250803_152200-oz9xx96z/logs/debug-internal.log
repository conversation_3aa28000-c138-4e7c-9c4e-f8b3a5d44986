{"time":"2025-08-03T15:22:00.865130546+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T15:22:01.025377513+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T15:22:01.02553243+08:00","level":"INFO","msg":"stream: created new stream","id":"oz9xx96z"}
{"time":"2025-08-03T15:22:01.025568143+08:00","level":"INFO","msg":"stream: started","id":"oz9xx96z"}
{"time":"2025-08-03T15:22:01.02564212+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"oz9xx96z"}
{"time":"2025-08-03T15:22:01.025712399+08:00","level":"INFO","msg":"sender: started","stream_id":"oz9xx96z"}
{"time":"2025-08-03T15:22:01.025796162+08:00","level":"INFO","msg":"handler: started","stream_id":"oz9xx96z"}
{"time":"2025-08-03T15:22:01.026266341+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T15:27:45.514914952+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T15:27:45.518219606+08:00","level":"INFO","msg":"stream: closing","id":"oz9xx96z"}
{"time":"2025-08-03T15:27:45.518246798+08:00","level":"INFO","msg":"handler: closed","stream_id":"oz9xx96z"}
{"time":"2025-08-03T15:27:45.518260955+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"oz9xx96z"}
{"time":"2025-08-03T15:27:45.518332765+08:00","level":"INFO","msg":"sender: closed","stream_id":"oz9xx96z"}
{"time":"2025-08-03T15:27:45.518425826+08:00","level":"INFO","msg":"stream: closed","id":"oz9xx96z"}
