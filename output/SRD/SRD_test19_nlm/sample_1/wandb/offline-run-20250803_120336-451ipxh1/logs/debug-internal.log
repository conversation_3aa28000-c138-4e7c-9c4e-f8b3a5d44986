{"time":"2025-08-03T12:03:37.185495896+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T12:03:37.346545277+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T12:03:37.346699944+08:00","level":"INFO","msg":"stream: created new stream","id":"451ipxh1"}
{"time":"2025-08-03T12:03:37.346734312+08:00","level":"INFO","msg":"stream: started","id":"451ipxh1"}
{"time":"2025-08-03T12:03:37.34682681+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"451ipxh1"}
{"time":"2025-08-03T12:03:37.346913131+08:00","level":"INFO","msg":"handler: started","stream_id":"451ipxh1"}
{"time":"2025-08-03T12:03:37.346957424+08:00","level":"INFO","msg":"sender: started","stream_id":"451ipxh1"}
{"time":"2025-08-03T12:03:37.347857227+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T12:08:51.662452251+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T12:08:51.666295038+08:00","level":"INFO","msg":"stream: closing","id":"451ipxh1"}
{"time":"2025-08-03T12:08:51.666318013+08:00","level":"INFO","msg":"handler: closed","stream_id":"451ipxh1"}
{"time":"2025-08-03T12:08:51.666331222+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"451ipxh1"}
{"time":"2025-08-03T12:08:51.666339267+08:00","level":"INFO","msg":"sender: closed","stream_id":"451ipxh1"}
{"time":"2025-08-03T12:08:51.666424379+08:00","level":"INFO","msg":"stream: closed","id":"451ipxh1"}
