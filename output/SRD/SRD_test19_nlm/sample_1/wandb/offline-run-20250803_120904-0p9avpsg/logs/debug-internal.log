{"time":"2025-08-03T12:09:04.91470278+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T12:09:05.063976556+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T12:09:05.06409101+08:00","level":"INFO","msg":"stream: created new stream","id":"0p9avpsg"}
{"time":"2025-08-03T12:09:05.064132871+08:00","level":"INFO","msg":"stream: started","id":"0p9avpsg"}
{"time":"2025-08-03T12:09:05.064224744+08:00","level":"INFO","msg":"sender: started","stream_id":"0p9avpsg"}
{"time":"2025-08-03T12:09:05.06422482+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"0p9avpsg"}
{"time":"2025-08-03T12:09:05.064261869+08:00","level":"INFO","msg":"handler: started","stream_id":"0p9avpsg"}
{"time":"2025-08-03T12:09:05.064608733+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T12:16:33.192582589+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T12:16:33.19567507+08:00","level":"INFO","msg":"stream: closing","id":"0p9avpsg"}
{"time":"2025-08-03T12:16:33.195709756+08:00","level":"INFO","msg":"handler: closed","stream_id":"0p9avpsg"}
{"time":"2025-08-03T12:16:33.195731758+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"0p9avpsg"}
{"time":"2025-08-03T12:16:33.195800091+08:00","level":"INFO","msg":"sender: closed","stream_id":"0p9avpsg"}
{"time":"2025-08-03T12:16:33.195867148+08:00","level":"INFO","msg":"stream: closed","id":"0p9avpsg"}
