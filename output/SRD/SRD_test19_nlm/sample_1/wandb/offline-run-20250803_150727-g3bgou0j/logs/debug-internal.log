{"time":"2025-08-03T15:07:27.678297417+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T15:07:27.85828864+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T15:07:27.858474474+08:00","level":"INFO","msg":"stream: created new stream","id":"g3bgou0j"}
{"time":"2025-08-03T15:07:27.858507693+08:00","level":"INFO","msg":"stream: started","id":"g3bgou0j"}
{"time":"2025-08-03T15:07:27.858604364+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"g3bgou0j"}
{"time":"2025-08-03T15:07:27.85869945+08:00","level":"INFO","msg":"handler: started","stream_id":"g3bgou0j"}
{"time":"2025-08-03T15:07:27.858770875+08:00","level":"INFO","msg":"sender: started","stream_id":"g3bgou0j"}
{"time":"2025-08-03T15:07:27.859602464+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T15:12:13.212762633+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T15:12:13.216857234+08:00","level":"INFO","msg":"stream: closing","id":"g3bgou0j"}
{"time":"2025-08-03T15:12:13.216874044+08:00","level":"INFO","msg":"handler: closed","stream_id":"g3bgou0j"}
{"time":"2025-08-03T15:12:13.216885162+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"g3bgou0j"}
{"time":"2025-08-03T15:12:13.216895015+08:00","level":"INFO","msg":"sender: closed","stream_id":"g3bgou0j"}
{"time":"2025-08-03T15:12:13.216959454+08:00","level":"INFO","msg":"stream: closed","id":"g3bgou0j"}
