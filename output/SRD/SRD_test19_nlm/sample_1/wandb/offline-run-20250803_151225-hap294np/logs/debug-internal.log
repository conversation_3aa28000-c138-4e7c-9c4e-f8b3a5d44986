{"time":"2025-08-03T15:12:25.222268881+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T15:12:25.37969457+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T15:12:25.379835168+08:00","level":"INFO","msg":"stream: created new stream","id":"hap294np"}
{"time":"2025-08-03T15:12:25.379861471+08:00","level":"INFO","msg":"stream: started","id":"hap294np"}
{"time":"2025-08-03T15:12:25.37995929+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"hap294np"}
{"time":"2025-08-03T15:12:25.380037944+08:00","level":"INFO","msg":"sender: started","stream_id":"hap294np"}
{"time":"2025-08-03T15:12:25.380091075+08:00","level":"INFO","msg":"handler: started","stream_id":"hap294np"}
{"time":"2025-08-03T15:12:25.38046451+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T15:19:10.208989347+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T15:19:10.212215563+08:00","level":"INFO","msg":"stream: closing","id":"hap294np"}
{"time":"2025-08-03T15:19:10.212233735+08:00","level":"INFO","msg":"handler: closed","stream_id":"hap294np"}
{"time":"2025-08-03T15:19:10.21225039+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"hap294np"}
{"time":"2025-08-03T15:19:10.212315593+08:00","level":"INFO","msg":"sender: closed","stream_id":"hap294np"}
{"time":"2025-08-03T15:19:10.212372916+08:00","level":"INFO","msg":"stream: closed","id":"hap294np"}
