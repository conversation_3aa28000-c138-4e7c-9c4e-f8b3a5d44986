{"time":"2025-07-28T21:49:17.179025341+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test16_GN_PM/sample_1/wandb/offline-run-20250728_214917-rtyaaabh/logs/debug-core.log"}
{"time":"2025-07-28T21:49:17.2879581+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T21:49:17.288060726+08:00","level":"INFO","msg":"stream: created new stream","id":"rtyaaabh"}
{"time":"2025-07-28T21:49:17.288081863+08:00","level":"INFO","msg":"stream: started","id":"rtyaaabh"}
{"time":"2025-07-28T21:49:17.288166726+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"rtyaaabh"}
{"time":"2025-07-28T21:49:17.288240914+08:00","level":"INFO","msg":"sender: started","stream_id":"rtyaaabh"}
{"time":"2025-07-28T21:49:17.288216267+08:00","level":"INFO","msg":"handler: started","stream_id":"rtyaaabh"}
{"time":"2025-07-28T21:49:17.293700396+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T21:55:06.102849039+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T21:55:06.102941712+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T21:55:06.104183196+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T21:55:06.107824589+08:00","level":"INFO","msg":"stream: closing","id":"rtyaaabh"}
{"time":"2025-07-28T21:55:06.107846115+08:00","level":"INFO","msg":"handler: closed","stream_id":"rtyaaabh"}
{"time":"2025-07-28T21:55:06.10785738+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"rtyaaabh"}
{"time":"2025-07-28T21:55:06.107920819+08:00","level":"INFO","msg":"sender: closed","stream_id":"rtyaaabh"}
{"time":"2025-07-28T21:55:06.107955153+08:00","level":"INFO","msg":"stream: closed","id":"rtyaaabh"}
