{"time":"2025-07-28T22:49:49.330724539+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test16_GN_PM/sample_1/wandb/offline-run-20250728_224949-29ufuqxb/logs/debug-core.log"}
{"time":"2025-07-28T22:49:49.440750053+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:49:49.440885497+08:00","level":"INFO","msg":"stream: created new stream","id":"29ufuqxb"}
{"time":"2025-07-28T22:49:49.440913269+08:00","level":"INFO","msg":"stream: started","id":"29ufuqxb"}
{"time":"2025-07-28T22:49:49.441003642+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"29ufuqxb"}
{"time":"2025-07-28T22:49:49.441078259+08:00","level":"INFO","msg":"sender: started","stream_id":"29ufuqxb"}
{"time":"2025-07-28T22:49:49.441123309+08:00","level":"INFO","msg":"handler: started","stream_id":"29ufuqxb"}
{"time":"2025-07-28T22:49:49.444837349+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:55:30.949034026+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:55:30.949158337+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:55:30.950043877+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T22:55:30.953199571+08:00","level":"INFO","msg":"stream: closing","id":"29ufuqxb"}
{"time":"2025-07-28T22:55:30.953243092+08:00","level":"INFO","msg":"handler: closed","stream_id":"29ufuqxb"}
{"time":"2025-07-28T22:55:30.953259052+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"29ufuqxb"}
{"time":"2025-07-28T22:55:30.953297442+08:00","level":"INFO","msg":"sender: closed","stream_id":"29ufuqxb"}
{"time":"2025-07-28T22:55:30.953376131+08:00","level":"INFO","msg":"stream: closed","id":"29ufuqxb"}
