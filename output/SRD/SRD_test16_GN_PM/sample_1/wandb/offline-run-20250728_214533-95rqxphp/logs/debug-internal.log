{"time":"2025-07-28T21:45:34.171745365+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test16_GN_PM/sample_1/wandb/offline-run-20250728_214533-95rqxphp/logs/debug-core.log"}
{"time":"2025-07-28T21:45:34.283696319+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T21:45:34.283852668+08:00","level":"INFO","msg":"stream: created new stream","id":"95rqxphp"}
{"time":"2025-07-28T21:45:34.283876308+08:00","level":"INFO","msg":"stream: started","id":"95rqxphp"}
{"time":"2025-07-28T21:45:34.283965211+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"95rqxphp"}
{"time":"2025-07-28T21:45:34.284000679+08:00","level":"INFO","msg":"handler: started","stream_id":"95rqxphp"}
{"time":"2025-07-28T21:45:34.284043936+08:00","level":"INFO","msg":"sender: started","stream_id":"95rqxphp"}
{"time":"2025-07-28T21:45:34.290379476+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T21:49:14.778177206+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T21:49:14.778247242+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T21:49:14.779133853+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T21:49:14.783695213+08:00","level":"INFO","msg":"stream: closing","id":"95rqxphp"}
{"time":"2025-07-28T21:49:14.783723813+08:00","level":"INFO","msg":"handler: closed","stream_id":"95rqxphp"}
{"time":"2025-07-28T21:49:14.783738179+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"95rqxphp"}
{"time":"2025-07-28T21:49:14.783749335+08:00","level":"INFO","msg":"sender: closed","stream_id":"95rqxphp"}
{"time":"2025-07-28T21:49:14.783843923+08:00","level":"INFO","msg":"stream: closed","id":"95rqxphp"}
