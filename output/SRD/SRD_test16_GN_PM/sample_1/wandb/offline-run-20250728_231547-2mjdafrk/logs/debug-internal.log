{"time":"2025-07-28T23:15:47.778300733+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test16_GN_PM/sample_1/wandb/offline-run-20250728_231547-2mjdafrk/logs/debug-core.log"}
{"time":"2025-07-28T23:15:47.889140124+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T23:15:47.889302146+08:00","level":"INFO","msg":"stream: created new stream","id":"2mjdafrk"}
{"time":"2025-07-28T23:15:47.889327229+08:00","level":"INFO","msg":"stream: started","id":"2mjdafrk"}
{"time":"2025-07-28T23:15:47.889415631+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"2mjdafrk"}
{"time":"2025-07-28T23:15:47.889491426+08:00","level":"INFO","msg":"handler: started","stream_id":"2mjdafrk"}
{"time":"2025-07-28T23:15:47.889538633+08:00","level":"INFO","msg":"sender: started","stream_id":"2mjdafrk"}
{"time":"2025-07-28T23:15:47.896451611+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T23:16:24.938087513+08:00","level":"INFO","msg":"stream: closing","id":"2mjdafrk"}
{"time":"2025-07-28T23:16:24.93819364+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T23:16:24.938263281+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T23:16:24.938411668+08:00","level":"INFO","msg":"handler: closed","stream_id":"2mjdafrk"}
{"time":"2025-07-28T23:16:24.938431688+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"2mjdafrk"}
{"time":"2025-07-28T23:16:24.938456982+08:00","level":"INFO","msg":"sender: closed","stream_id":"2mjdafrk"}
{"time":"2025-07-28T23:16:24.938569972+08:00","level":"INFO","msg":"stream: closed","id":"2mjdafrk"}
