{"time":"2025-07-28T22:46:14.787352085+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test16_GN_PM/sample_1/wandb/offline-run-20250728_224614-zjhw2ybb/logs/debug-core.log"}
{"time":"2025-07-28T22:46:14.898296618+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:46:14.898456264+08:00","level":"INFO","msg":"stream: created new stream","id":"zjhw2ybb"}
{"time":"2025-07-28T22:46:14.898482916+08:00","level":"INFO","msg":"stream: started","id":"zjhw2ybb"}
{"time":"2025-07-28T22:46:14.898594813+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"zjhw2ybb"}
{"time":"2025-07-28T22:46:14.898664386+08:00","level":"INFO","msg":"handler: started","stream_id":"zjhw2ybb"}
{"time":"2025-07-28T22:46:14.898728086+08:00","level":"INFO","msg":"sender: started","stream_id":"zjhw2ybb"}
{"time":"2025-07-28T22:46:14.904841721+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:49:47.258895431+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:49:47.259019965+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:49:47.26019821+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T22:49:47.264795583+08:00","level":"INFO","msg":"stream: closing","id":"zjhw2ybb"}
{"time":"2025-07-28T22:49:47.264815804+08:00","level":"INFO","msg":"handler: closed","stream_id":"zjhw2ybb"}
{"time":"2025-07-28T22:49:47.264839269+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"zjhw2ybb"}
{"time":"2025-07-28T22:49:47.264890061+08:00","level":"INFO","msg":"sender: closed","stream_id":"zjhw2ybb"}
{"time":"2025-07-28T22:49:47.264937747+08:00","level":"INFO","msg":"stream: closed","id":"zjhw2ybb"}
