{"time":"2025-08-03T12:03:12.089290085+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T12:03:12.253546271+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T12:03:12.253690655+08:00","level":"INFO","msg":"stream: created new stream","id":"6tsx6r2b"}
{"time":"2025-08-03T12:03:12.253714815+08:00","level":"INFO","msg":"stream: started","id":"6tsx6r2b"}
{"time":"2025-08-03T12:03:12.253764243+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"6tsx6r2b"}
{"time":"2025-08-03T12:03:12.253858823+08:00","level":"INFO","msg":"sender: started","stream_id":"6tsx6r2b"}
{"time":"2025-08-03T12:03:12.253917543+08:00","level":"INFO","msg":"handler: started","stream_id":"6tsx6r2b"}
{"time":"2025-08-03T12:03:12.254529479+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T12:09:36.155201949+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T12:09:36.159267377+08:00","level":"INFO","msg":"stream: closing","id":"6tsx6r2b"}
{"time":"2025-08-03T12:09:36.159286332+08:00","level":"INFO","msg":"handler: closed","stream_id":"6tsx6r2b"}
{"time":"2025-08-03T12:09:36.159296507+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"6tsx6r2b"}
{"time":"2025-08-03T12:09:36.159344703+08:00","level":"INFO","msg":"sender: closed","stream_id":"6tsx6r2b"}
{"time":"2025-08-03T12:09:36.159418892+08:00","level":"INFO","msg":"stream: closed","id":"6tsx6r2b"}
