{"time":"2025-08-03T12:09:59.769338687+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T12:09:59.930743094+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T12:09:59.930842525+08:00","level":"INFO","msg":"stream: created new stream","id":"nr2o3h2f"}
{"time":"2025-08-03T12:09:59.930864488+08:00","level":"INFO","msg":"stream: started","id":"nr2o3h2f"}
{"time":"2025-08-03T12:09:59.930901567+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"nr2o3h2f"}
{"time":"2025-08-03T12:09:59.930979647+08:00","level":"INFO","msg":"sender: started","stream_id":"nr2o3h2f"}
{"time":"2025-08-03T12:09:59.931022518+08:00","level":"INFO","msg":"handler: started","stream_id":"nr2o3h2f"}
{"time":"2025-08-03T12:09:59.931319974+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T12:18:44.73359077+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T12:18:44.736729128+08:00","level":"INFO","msg":"stream: closing","id":"nr2o3h2f"}
{"time":"2025-08-03T12:18:44.736758709+08:00","level":"INFO","msg":"handler: closed","stream_id":"nr2o3h2f"}
{"time":"2025-08-03T12:18:44.736783526+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"nr2o3h2f"}
{"time":"2025-08-03T12:18:44.736843161+08:00","level":"INFO","msg":"sender: closed","stream_id":"nr2o3h2f"}
{"time":"2025-08-03T12:18:44.73690719+08:00","level":"INFO","msg":"stream: closed","id":"nr2o3h2f"}
