{"time":"2025-08-04T15:59:14.094760116+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:59:14.254068552+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:59:14.25429063+08:00","level":"INFO","msg":"stream: created new stream","id":"7nbhg4k6"}
{"time":"2025-08-04T15:59:14.254315931+08:00","level":"INFO","msg":"stream: started","id":"7nbhg4k6"}
{"time":"2025-08-04T15:59:14.254397895+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"7nbhg4k6"}
{"time":"2025-08-04T15:59:14.254467609+08:00","level":"INFO","msg":"sender: started","stream_id":"7nbhg4k6"}
{"time":"2025-08-04T15:59:14.254470417+08:00","level":"INFO","msg":"handler: started","stream_id":"7nbhg4k6"}
{"time":"2025-08-04T15:59:14.255423956+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T16:04:08.943353006+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T16:04:08.947972923+08:00","level":"INFO","msg":"stream: closing","id":"7nbhg4k6"}
{"time":"2025-08-04T16:04:08.948012124+08:00","level":"INFO","msg":"handler: closed","stream_id":"7nbhg4k6"}
{"time":"2025-08-04T16:04:08.948027147+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"7nbhg4k6"}
{"time":"2025-08-04T16:04:08.948075373+08:00","level":"INFO","msg":"sender: closed","stream_id":"7nbhg4k6"}
{"time":"2025-08-04T16:04:08.948180179+08:00","level":"INFO","msg":"stream: closed","id":"7nbhg4k6"}
