{"time":"2025-08-04T15:31:58.356677939+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:31:58.518357595+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:31:58.51851671+08:00","level":"INFO","msg":"stream: created new stream","id":"xvh5gp68"}
{"time":"2025-08-04T15:31:58.518542264+08:00","level":"INFO","msg":"stream: started","id":"xvh5gp68"}
{"time":"2025-08-04T15:31:58.518599544+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"xvh5gp68"}
{"time":"2025-08-04T15:31:58.518695931+08:00","level":"INFO","msg":"sender: started","stream_id":"xvh5gp68"}
{"time":"2025-08-04T15:31:58.518699069+08:00","level":"INFO","msg":"handler: started","stream_id":"xvh5gp68"}
{"time":"2025-08-04T15:31:58.519297668+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:33:48.644780746+08:00","level":"INFO","msg":"stream: closing","id":"xvh5gp68"}
{"time":"2025-08-04T15:33:48.645163668+08:00","level":"INFO","msg":"handler: closed","stream_id":"xvh5gp68"}
{"time":"2025-08-04T15:33:48.645185332+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"xvh5gp68"}
{"time":"2025-08-04T15:33:48.645208272+08:00","level":"INFO","msg":"sender: closed","stream_id":"xvh5gp68"}
{"time":"2025-08-04T15:33:48.645282796+08:00","level":"INFO","msg":"stream: closed","id":"xvh5gp68"}
