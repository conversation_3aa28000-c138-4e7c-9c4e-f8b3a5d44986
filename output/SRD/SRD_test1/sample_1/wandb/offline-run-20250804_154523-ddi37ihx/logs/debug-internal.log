{"time":"2025-08-04T15:45:23.648540769+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:45:23.80256621+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:45:23.802707851+08:00","level":"INFO","msg":"stream: created new stream","id":"ddi37ihx"}
{"time":"2025-08-04T15:45:23.802731744+08:00","level":"INFO","msg":"stream: started","id":"ddi37ihx"}
{"time":"2025-08-04T15:45:23.8028429+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ddi37ihx"}
{"time":"2025-08-04T15:45:23.802924598+08:00","level":"INFO","msg":"handler: started","stream_id":"ddi37ihx"}
{"time":"2025-08-04T15:45:23.802969935+08:00","level":"INFO","msg":"sender: started","stream_id":"ddi37ihx"}
{"time":"2025-08-04T15:45:23.80413081+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:47:01.565867566+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T15:47:01.570420101+08:00","level":"INFO","msg":"stream: closing","id":"ddi37ihx"}
{"time":"2025-08-04T15:47:01.570451001+08:00","level":"INFO","msg":"handler: closed","stream_id":"ddi37ihx"}
{"time":"2025-08-04T15:47:01.570467731+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"ddi37ihx"}
{"time":"2025-08-04T15:47:01.570546166+08:00","level":"INFO","msg":"sender: closed","stream_id":"ddi37ihx"}
{"time":"2025-08-04T15:47:01.570585189+08:00","level":"INFO","msg":"stream: closed","id":"ddi37ihx"}
