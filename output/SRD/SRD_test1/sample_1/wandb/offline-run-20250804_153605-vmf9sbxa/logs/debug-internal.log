{"time":"2025-08-04T15:36:05.977434371+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:36:06.133974779+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:36:06.134176986+08:00","level":"INFO","msg":"stream: created new stream","id":"vmf9sbxa"}
{"time":"2025-08-04T15:36:06.134215104+08:00","level":"INFO","msg":"stream: started","id":"vmf9sbxa"}
{"time":"2025-08-04T15:36:06.134345545+08:00","level":"INFO","msg":"handler: started","stream_id":"vmf9sbxa"}
{"time":"2025-08-04T15:36:06.1343146+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"vmf9sbxa"}
{"time":"2025-08-04T15:36:06.134344814+08:00","level":"INFO","msg":"sender: started","stream_id":"vmf9sbxa"}
{"time":"2025-08-04T15:36:06.135493706+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:39:16.558292853+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T15:39:16.562604289+08:00","level":"INFO","msg":"stream: closing","id":"vmf9sbxa"}
{"time":"2025-08-04T15:39:16.562624754+08:00","level":"INFO","msg":"handler: closed","stream_id":"vmf9sbxa"}
{"time":"2025-08-04T15:39:16.562635428+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"vmf9sbxa"}
{"time":"2025-08-04T15:39:16.562689663+08:00","level":"INFO","msg":"sender: closed","stream_id":"vmf9sbxa"}
{"time":"2025-08-04T15:39:16.562721223+08:00","level":"INFO","msg":"stream: closed","id":"vmf9sbxa"}
