{"time":"2025-08-04T15:22:47.513761248+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:22:47.681944197+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:22:47.682441771+08:00","level":"INFO","msg":"stream: created new stream","id":"4t21nv33"}
{"time":"2025-08-04T15:22:47.682470998+08:00","level":"INFO","msg":"stream: started","id":"4t21nv33"}
{"time":"2025-08-04T15:22:47.682585056+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"4t21nv33"}
{"time":"2025-08-04T15:22:47.682647663+08:00","level":"INFO","msg":"handler: started","stream_id":"4t21nv33"}
{"time":"2025-08-04T15:22:47.682654853+08:00","level":"INFO","msg":"sender: started","stream_id":"4t21nv33"}
{"time":"2025-08-04T15:22:47.68361259+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:23:01.705182839+08:00","level":"INFO","msg":"stream: closing","id":"4t21nv33"}
{"time":"2025-08-04T15:23:01.705371747+08:00","level":"INFO","msg":"handler: closed","stream_id":"4t21nv33"}
{"time":"2025-08-04T15:23:01.705396675+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"4t21nv33"}
{"time":"2025-08-04T15:23:01.705451465+08:00","level":"INFO","msg":"sender: closed","stream_id":"4t21nv33"}
{"time":"2025-08-04T15:23:01.705504446+08:00","level":"INFO","msg":"stream: closed","id":"4t21nv33"}
