{"time":"2025-08-03T16:28:42.19243601+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T16:28:42.358235452+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T16:28:42.358432954+08:00","level":"INFO","msg":"stream: created new stream","id":"t00bt48k"}
{"time":"2025-08-03T16:28:42.358457227+08:00","level":"INFO","msg":"stream: started","id":"t00bt48k"}
{"time":"2025-08-03T16:28:42.358548895+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"t00bt48k"}
{"time":"2025-08-03T16:28:42.35861087+08:00","level":"INFO","msg":"handler: started","stream_id":"t00bt48k"}
{"time":"2025-08-03T16:28:42.358656932+08:00","level":"INFO","msg":"sender: started","stream_id":"t00bt48k"}
{"time":"2025-08-03T16:28:42.359084+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T16:30:42.494818582+08:00","level":"INFO","msg":"stream: closing","id":"t00bt48k"}
{"time":"2025-08-03T16:30:42.495274212+08:00","level":"INFO","msg":"handler: closed","stream_id":"t00bt48k"}
{"time":"2025-08-03T16:30:42.495336963+08:00","level":"INFO","msg":"sender: closed","stream_id":"t00bt48k"}
{"time":"2025-08-03T16:30:42.495346213+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"t00bt48k"}
{"time":"2025-08-03T16:30:42.495588308+08:00","level":"INFO","msg":"stream: closed","id":"t00bt48k"}
