{"time":"2025-08-04T15:47:25.237789514+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:47:25.402920056+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:47:25.403047088+08:00","level":"INFO","msg":"stream: created new stream","id":"zr8q3hzz"}
{"time":"2025-08-04T15:47:25.403075115+08:00","level":"INFO","msg":"stream: started","id":"zr8q3hzz"}
{"time":"2025-08-04T15:47:25.403184935+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"zr8q3hzz"}
{"time":"2025-08-04T15:47:25.40323891+08:00","level":"INFO","msg":"handler: started","stream_id":"zr8q3hzz"}
{"time":"2025-08-04T15:47:25.403315646+08:00","level":"INFO","msg":"sender: started","stream_id":"zr8q3hzz"}
{"time":"2025-08-04T15:47:25.403850003+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:50:17.626550034+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T15:50:17.63032813+08:00","level":"INFO","msg":"stream: closing","id":"zr8q3hzz"}
{"time":"2025-08-04T15:50:17.630355184+08:00","level":"INFO","msg":"handler: closed","stream_id":"zr8q3hzz"}
{"time":"2025-08-04T15:50:17.630368973+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"zr8q3hzz"}
{"time":"2025-08-04T15:50:17.630401736+08:00","level":"INFO","msg":"sender: closed","stream_id":"zr8q3hzz"}
{"time":"2025-08-04T15:50:17.630465902+08:00","level":"INFO","msg":"stream: closed","id":"zr8q3hzz"}
