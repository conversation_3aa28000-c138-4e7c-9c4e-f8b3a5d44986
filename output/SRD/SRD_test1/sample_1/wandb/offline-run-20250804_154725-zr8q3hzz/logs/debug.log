2025-08-04 15:47:25,232 INFO    MainThread:31656 [wandb_init.py:setup_run_log_directory():703] Logging user logs to output/SRD/SRD_test1/sample_1/wandb/offline-run-20250804_154725-zr8q3hzz/logs/debug.log
2025-08-04 15:47:25,232 INFO    MainThread:31656 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to output/SRD/SRD_test1/sample_1/wandb/offline-run-20250804_154725-zr8q3hzz/logs/debug-internal.log
2025-08-04 15:47:25,232 INFO    MainThread:31656 [wandb_init.py:init():830] calling init triggers
2025-08-04 15:47:25,232 INFO    MainThread:31656 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'data': {'dataset': <class 'dataloader.datasets.Dataset_random_patch'>, 'config': <exps.configs.datasets.SRD.Config object at 0x7fb8134c82f0>}, 'model': {'net': <class 'models.SRD.SRD.SRD'>, 'denoiser_in_ch': 3, 'out_ch': 1, 'sr_num': 1, 'base_ch': 4, 'dt_rank': 32, 'd_state': 32, 'depth': 2, 'vim_block_num': 2, 'dropout': 0.1, 'wavename': 'haar'}, 'dataloader': {'train': {'batch_size': 1, 'num_workers': 1}, 'val': {'batch_size': 1, 'num_workers': 1}, 'test': {'batch_size': 1, 'num_workers': 1}}, 'exp': {'project_name': 'SRD', 'exp_name': 'SRD_test1', 'gpu': '1', 'path_to_init': [PosixPath('output/SRD/SRD_test1/sample_1')], 'script': 'exps.scripts.SRD', 'script_func': {'train_denoiser': 'train', 'infer_denoiser': 'test', 'train_SR': 'train', 'infer_SR_one_double': 'test'}, 'train_dataset': 1, 'test_dataset': 1, 'save_path': PosixPath('output/SRD/SRD_test1/sample_1'), 'split_size': 32, 'compute_metrics': False, 'debug': False, 'denoise_iters': 50, 'sr_iters': 50, 'val_freq': 10, 'infer_denoiser_name': 'minLoss', 'infer_SR_name': 'final'}, 'optim': {'lr': 0.001, 'weight_decay': 0.0001}, '_wandb': {}}
2025-08-04 15:47:25,233 INFO    MainThread:31656 [wandb_init.py:init():871] starting backend
2025-08-04 15:47:25,233 INFO    MainThread:31656 [wandb_init.py:init():874] sending inform_init request
2025-08-04 15:47:25,234 INFO    MainThread:31656 [wandb_init.py:init():882] backend started and connected
2025-08-04 15:47:25,237 INFO    MainThread:31656 [wandb_init.py:init():953] updated telemetry
2025-08-04 15:47:25,238 INFO    MainThread:31656 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-04 15:47:25,406 INFO    MainThread:31656 [wandb_init.py:init():1029] starting run threads in backend
2025-08-04 15:47:25,512 INFO    MainThread:31656 [wandb_run.py:_console_start():2458] atexit reg
2025-08-04 15:47:25,512 INFO    MainThread:31656 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-04 15:47:25,512 INFO    MainThread:31656 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-04 15:47:25,512 INFO    MainThread:31656 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-04 15:47:25,513 INFO    MainThread:31656 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-04 15:50:17,624 INFO    MainThread:31656 [wandb_run.py:_finish():2224] finishing run YangBing_Team/SRD/zr8q3hzz
2025-08-04 15:50:17,625 INFO    MainThread:31656 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-08-04 15:50:17,625 INFO    MainThread:31656 [wandb_run.py:_restore():2405] restore
2025-08-04 15:50:17,625 INFO    MainThread:31656 [wandb_run.py:_restore():2411] restore done
2025-08-04 15:50:17,628 INFO    MainThread:31656 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-08-04 15:50:17,629 INFO    MainThread:31656 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
