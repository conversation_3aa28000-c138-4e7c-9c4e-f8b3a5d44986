{"time":"2025-08-04T15:39:38.62168528+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:39:38.780795408+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:39:38.78092426+08:00","level":"INFO","msg":"stream: created new stream","id":"e3uxqjck"}
{"time":"2025-08-04T15:39:38.780953369+08:00","level":"INFO","msg":"stream: started","id":"e3uxqjck"}
{"time":"2025-08-04T15:39:38.781041451+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"e3uxqjck"}
{"time":"2025-08-04T15:39:38.781131771+08:00","level":"INFO","msg":"sender: started","stream_id":"e3uxqjck"}
{"time":"2025-08-04T15:39:38.781177498+08:00","level":"INFO","msg":"handler: started","stream_id":"e3uxqjck"}
{"time":"2025-08-04T15:39:38.781613374+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:44:47.188594368+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T15:44:47.192613878+08:00","level":"INFO","msg":"stream: closing","id":"e3uxqjck"}
{"time":"2025-08-04T15:44:47.192647387+08:00","level":"INFO","msg":"handler: closed","stream_id":"e3uxqjck"}
{"time":"2025-08-04T15:44:47.192668004+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"e3uxqjck"}
{"time":"2025-08-04T15:44:47.192722139+08:00","level":"INFO","msg":"sender: closed","stream_id":"e3uxqjck"}
{"time":"2025-08-04T15:44:47.192778057+08:00","level":"INFO","msg":"stream: closed","id":"e3uxqjck"}
