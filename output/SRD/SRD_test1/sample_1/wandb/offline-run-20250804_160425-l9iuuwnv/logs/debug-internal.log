{"time":"2025-08-04T16:04:25.890810571+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T16:04:26.044770906+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T16:04:26.044936692+08:00","level":"INFO","msg":"stream: created new stream","id":"l9iuuwnv"}
{"time":"2025-08-04T16:04:26.044990069+08:00","level":"INFO","msg":"stream: started","id":"l9iuuwnv"}
{"time":"2025-08-04T16:04:26.045049188+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"l9iuuwnv"}
{"time":"2025-08-04T16:04:26.045093309+08:00","level":"INFO","msg":"handler: started","stream_id":"l9iuuwnv"}
{"time":"2025-08-04T16:04:26.045182612+08:00","level":"INFO","msg":"sender: started","stream_id":"l9iuuwnv"}
{"time":"2025-08-04T16:04:26.045540325+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T16:12:12.904228413+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T16:12:12.907644012+08:00","level":"INFO","msg":"stream: closing","id":"l9iuuwnv"}
{"time":"2025-08-04T16:12:12.907673072+08:00","level":"INFO","msg":"handler: closed","stream_id":"l9iuuwnv"}
{"time":"2025-08-04T16:12:12.907695219+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"l9iuuwnv"}
{"time":"2025-08-04T16:12:12.90775444+08:00","level":"INFO","msg":"sender: closed","stream_id":"l9iuuwnv"}
{"time":"2025-08-04T16:12:12.90781933+08:00","level":"INFO","msg":"stream: closed","id":"l9iuuwnv"}
