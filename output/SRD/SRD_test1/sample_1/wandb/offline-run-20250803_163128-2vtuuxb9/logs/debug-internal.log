{"time":"2025-08-03T16:31:28.586171952+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T16:31:28.741878148+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T16:31:28.742041279+08:00","level":"INFO","msg":"stream: created new stream","id":"2vtuuxb9"}
{"time":"2025-08-03T16:31:28.742070132+08:00","level":"INFO","msg":"stream: started","id":"2vtuuxb9"}
{"time":"2025-08-03T16:31:28.74217433+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"2vtuuxb9"}
{"time":"2025-08-03T16:31:28.742247269+08:00","level":"INFO","msg":"sender: started","stream_id":"2vtuuxb9"}
{"time":"2025-08-03T16:31:28.742284988+08:00","level":"INFO","msg":"handler: started","stream_id":"2vtuuxb9"}
{"time":"2025-08-03T16:31:28.743222333+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T16:34:38.915205197+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T16:34:38.919818086+08:00","level":"INFO","msg":"stream: closing","id":"2vtuuxb9"}
{"time":"2025-08-03T16:34:38.919839824+08:00","level":"INFO","msg":"handler: closed","stream_id":"2vtuuxb9"}
{"time":"2025-08-03T16:34:38.919850614+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"2vtuuxb9"}
{"time":"2025-08-03T16:34:38.919902841+08:00","level":"INFO","msg":"sender: closed","stream_id":"2vtuuxb9"}
{"time":"2025-08-03T16:34:38.919962751+08:00","level":"INFO","msg":"stream: closed","id":"2vtuuxb9"}
