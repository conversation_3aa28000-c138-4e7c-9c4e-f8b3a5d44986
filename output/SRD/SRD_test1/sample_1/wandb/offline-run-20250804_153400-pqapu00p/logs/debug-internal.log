{"time":"2025-08-04T15:34:01.00984883+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:34:01.168333576+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:34:01.168498297+08:00","level":"INFO","msg":"stream: created new stream","id":"pqapu00p"}
{"time":"2025-08-04T15:34:01.16852641+08:00","level":"INFO","msg":"stream: started","id":"pqapu00p"}
{"time":"2025-08-04T15:34:01.168634528+08:00","level":"INFO","msg":"sender: started","stream_id":"pqapu00p"}
{"time":"2025-08-04T15:34:01.168694782+08:00","level":"INFO","msg":"handler: started","stream_id":"pqapu00p"}
{"time":"2025-08-04T15:34:01.168624556+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"pqapu00p"}
{"time":"2025-08-04T15:34:01.169703669+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:35:41.3095798+08:00","level":"INFO","msg":"stream: closing","id":"pqapu00p"}
{"time":"2025-08-04T15:35:41.309882425+08:00","level":"INFO","msg":"handler: closed","stream_id":"pqapu00p"}
{"time":"2025-08-04T15:35:41.309896305+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"pqapu00p"}
{"time":"2025-08-04T15:35:41.309913852+08:00","level":"INFO","msg":"sender: closed","stream_id":"pqapu00p"}
{"time":"2025-08-04T15:35:41.310300791+08:00","level":"INFO","msg":"stream: closed","id":"pqapu00p"}
