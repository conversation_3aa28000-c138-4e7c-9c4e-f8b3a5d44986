{"time":"2025-08-04T15:23:16.163891246+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:23:16.325592872+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:23:16.325765689+08:00","level":"INFO","msg":"stream: created new stream","id":"q1m8netb"}
{"time":"2025-08-04T15:23:16.325791361+08:00","level":"INFO","msg":"stream: started","id":"q1m8netb"}
{"time":"2025-08-04T15:23:16.325889318+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"q1m8netb"}
{"time":"2025-08-04T15:23:16.325978415+08:00","level":"INFO","msg":"handler: started","stream_id":"q1m8netb"}
{"time":"2025-08-04T15:23:16.326032242+08:00","level":"INFO","msg":"sender: started","stream_id":"q1m8netb"}
{"time":"2025-08-04T15:23:16.327019703+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:26:11.195797169+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T15:26:11.200168736+08:00","level":"INFO","msg":"stream: closing","id":"q1m8netb"}
{"time":"2025-08-04T15:26:11.200194985+08:00","level":"INFO","msg":"handler: closed","stream_id":"q1m8netb"}
{"time":"2025-08-04T15:26:11.200207849+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"q1m8netb"}
{"time":"2025-08-04T15:26:11.20021368+08:00","level":"INFO","msg":"sender: closed","stream_id":"q1m8netb"}
{"time":"2025-08-04T15:26:11.200292464+08:00","level":"INFO","msg":"stream: closed","id":"q1m8netb"}
