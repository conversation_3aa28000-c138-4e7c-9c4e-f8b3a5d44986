{"time":"2025-08-04T15:26:37.697394917+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:26:37.858780228+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:26:37.858935164+08:00","level":"INFO","msg":"stream: created new stream","id":"ab3b93oe"}
{"time":"2025-08-04T15:26:37.858971981+08:00","level":"INFO","msg":"stream: started","id":"ab3b93oe"}
{"time":"2025-08-04T15:26:37.859060048+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ab3b93oe"}
{"time":"2025-08-04T15:26:37.859149395+08:00","level":"INFO","msg":"sender: started","stream_id":"ab3b93oe"}
{"time":"2025-08-04T15:26:37.859220922+08:00","level":"INFO","msg":"handler: started","stream_id":"ab3b93oe"}
{"time":"2025-08-04T15:26:37.859634682+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:31:10.097903356+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T15:31:10.101540127+08:00","level":"INFO","msg":"stream: closing","id":"ab3b93oe"}
{"time":"2025-08-04T15:31:10.101580996+08:00","level":"INFO","msg":"handler: closed","stream_id":"ab3b93oe"}
{"time":"2025-08-04T15:31:10.101594602+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"ab3b93oe"}
{"time":"2025-08-04T15:31:10.101654575+08:00","level":"INFO","msg":"sender: closed","stream_id":"ab3b93oe"}
{"time":"2025-08-04T15:31:10.101706547+08:00","level":"INFO","msg":"stream: closed","id":"ab3b93oe"}
