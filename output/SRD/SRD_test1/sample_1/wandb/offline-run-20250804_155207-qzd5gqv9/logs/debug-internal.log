{"time":"2025-08-04T15:52:08.218115644+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:52:08.384330265+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:52:08.384499891+08:00","level":"INFO","msg":"stream: created new stream","id":"qzd5gqv9"}
{"time":"2025-08-04T15:52:08.384543741+08:00","level":"INFO","msg":"stream: started","id":"qzd5gqv9"}
{"time":"2025-08-04T15:52:08.384607814+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"qzd5gqv9"}
{"time":"2025-08-04T15:52:08.384693745+08:00","level":"INFO","msg":"sender: started","stream_id":"qzd5gqv9"}
{"time":"2025-08-04T15:52:08.384748625+08:00","level":"INFO","msg":"handler: started","stream_id":"qzd5gqv9"}
{"time":"2025-08-04T15:52:08.385794833+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:53:50.982713212+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T15:53:50.987564472+08:00","level":"INFO","msg":"stream: closing","id":"qzd5gqv9"}
{"time":"2025-08-04T15:53:50.987581509+08:00","level":"INFO","msg":"handler: closed","stream_id":"qzd5gqv9"}
{"time":"2025-08-04T15:53:50.987591423+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"qzd5gqv9"}
{"time":"2025-08-04T15:53:50.987639263+08:00","level":"INFO","msg":"sender: closed","stream_id":"qzd5gqv9"}
{"time":"2025-08-04T15:53:50.987649321+08:00","level":"INFO","msg":"stream: closed","id":"qzd5gqv9"}
