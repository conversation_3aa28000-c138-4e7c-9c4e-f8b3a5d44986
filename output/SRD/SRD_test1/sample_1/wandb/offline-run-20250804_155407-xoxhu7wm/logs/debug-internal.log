{"time":"2025-08-04T15:54:07.42800541+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-04T15:54:07.587966843+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-04T15:54:07.588087981+08:00","level":"INFO","msg":"stream: created new stream","id":"xoxhu7wm"}
{"time":"2025-08-04T15:54:07.588128511+08:00","level":"INFO","msg":"stream: started","id":"xoxhu7wm"}
{"time":"2025-08-04T15:54:07.588253726+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"xoxhu7wm"}
{"time":"2025-08-04T15:54:07.588319313+08:00","level":"INFO","msg":"sender: started","stream_id":"xoxhu7wm"}
{"time":"2025-08-04T15:54:07.58835043+08:00","level":"INFO","msg":"handler: started","stream_id":"xoxhu7wm"}
{"time":"2025-08-04T15:54:07.588904449+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-04T15:56:52.292228151+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-04T15:56:52.295209937+08:00","level":"INFO","msg":"stream: closing","id":"xoxhu7wm"}
{"time":"2025-08-04T15:56:52.295231182+08:00","level":"INFO","msg":"handler: closed","stream_id":"xoxhu7wm"}
{"time":"2025-08-04T15:56:52.295244346+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"xoxhu7wm"}
{"time":"2025-08-04T15:56:52.295256358+08:00","level":"INFO","msg":"sender: closed","stream_id":"xoxhu7wm"}
{"time":"2025-08-04T15:56:52.295324798+08:00","level":"INFO","msg":"stream: closed","id":"xoxhu7wm"}
