{"time":"2025-08-03T16:34:55.645780431+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T16:34:55.807981724+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T16:34:55.808145691+08:00","level":"INFO","msg":"stream: created new stream","id":"e2k11eo2"}
{"time":"2025-08-03T16:34:55.808193832+08:00","level":"INFO","msg":"stream: started","id":"e2k11eo2"}
{"time":"2025-08-03T16:34:55.808284515+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"e2k11eo2"}
{"time":"2025-08-03T16:34:55.808353314+08:00","level":"INFO","msg":"sender: started","stream_id":"e2k11eo2"}
{"time":"2025-08-03T16:34:55.808423625+08:00","level":"INFO","msg":"handler: started","stream_id":"e2k11eo2"}
{"time":"2025-08-03T16:34:55.808984616+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T16:39:50.311130107+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T16:39:50.313925588+08:00","level":"INFO","msg":"stream: closing","id":"e2k11eo2"}
{"time":"2025-08-03T16:39:50.313947283+08:00","level":"INFO","msg":"handler: closed","stream_id":"e2k11eo2"}
{"time":"2025-08-03T16:39:50.313960203+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"e2k11eo2"}
{"time":"2025-08-03T16:39:50.314031008+08:00","level":"INFO","msg":"sender: closed","stream_id":"e2k11eo2"}
{"time":"2025-08-03T16:39:50.314079781+08:00","level":"INFO","msg":"stream: closed","id":"e2k11eo2"}
