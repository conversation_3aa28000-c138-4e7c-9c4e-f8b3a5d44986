{"time":"2025-07-28T22:28:59.135542695+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test1_GN_PM/sample_1/wandb/offline-run-20250728_222859-3317xhay/logs/debug-core.log"}
{"time":"2025-07-28T22:28:59.245578363+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:28:59.24572384+08:00","level":"INFO","msg":"stream: created new stream","id":"3317xhay"}
{"time":"2025-07-28T22:28:59.245751207+08:00","level":"INFO","msg":"stream: started","id":"3317xhay"}
{"time":"2025-07-28T22:28:59.245840365+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"3317xhay"}
{"time":"2025-07-28T22:28:59.245910938+08:00","level":"INFO","msg":"sender: started","stream_id":"3317xhay"}
{"time":"2025-07-28T22:28:59.24596286+08:00","level":"INFO","msg":"handler: started","stream_id":"3317xhay"}
{"time":"2025-07-28T22:28:59.251016477+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:29:02.255628532+08:00","level":"INFO","msg":"stream: closing","id":"3317xhay"}
{"time":"2025-07-28T22:29:02.255692365+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:29:02.255783206+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:29:02.256057974+08:00","level":"INFO","msg":"handler: closed","stream_id":"3317xhay"}
{"time":"2025-07-28T22:29:02.256086921+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"3317xhay"}
{"time":"2025-07-28T22:29:02.256126868+08:00","level":"INFO","msg":"sender: closed","stream_id":"3317xhay"}
{"time":"2025-07-28T22:29:02.256218878+08:00","level":"INFO","msg":"stream: closed","id":"3317xhay"}
