{"time":"2025-07-28T22:21:56.416886693+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test1_GN_PM/sample_1/wandb/offline-run-20250728_222156-51rqxw28/logs/debug-core.log"}
{"time":"2025-07-28T22:21:56.530500308+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:21:56.530690582+08:00","level":"INFO","msg":"stream: created new stream","id":"51rqxw28"}
{"time":"2025-07-28T22:21:56.530718565+08:00","level":"INFO","msg":"stream: started","id":"51rqxw28"}
{"time":"2025-07-28T22:21:56.530772083+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"51rqxw28"}
{"time":"2025-07-28T22:21:56.530830066+08:00","level":"INFO","msg":"handler: started","stream_id":"51rqxw28"}
{"time":"2025-07-28T22:21:56.530894743+08:00","level":"INFO","msg":"sender: started","stream_id":"51rqxw28"}
{"time":"2025-07-28T22:21:56.537948806+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:24:53.409617608+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:24:53.409726753+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:24:53.41102282+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T22:24:53.415541106+08:00","level":"INFO","msg":"stream: closing","id":"51rqxw28"}
{"time":"2025-07-28T22:24:53.41556404+08:00","level":"INFO","msg":"handler: closed","stream_id":"51rqxw28"}
{"time":"2025-07-28T22:24:53.41557736+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"51rqxw28"}
{"time":"2025-07-28T22:24:53.415629776+08:00","level":"INFO","msg":"sender: closed","stream_id":"51rqxw28"}
{"time":"2025-07-28T22:24:53.415677407+08:00","level":"INFO","msg":"stream: closed","id":"51rqxw28"}
