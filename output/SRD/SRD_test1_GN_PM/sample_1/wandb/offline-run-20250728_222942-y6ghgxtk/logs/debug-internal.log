{"time":"2025-07-28T22:29:42.495191001+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test1_GN_PM/sample_1/wandb/offline-run-20250728_222942-y6ghgxtk/logs/debug-core.log"}
{"time":"2025-07-28T22:29:42.607704343+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:29:42.607914674+08:00","level":"INFO","msg":"stream: created new stream","id":"y6ghgxtk"}
{"time":"2025-07-28T22:29:42.607943759+08:00","level":"INFO","msg":"stream: started","id":"y6ghgxtk"}
{"time":"2025-07-28T22:29:42.608030305+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"y6ghgxtk"}
{"time":"2025-07-28T22:29:42.608096934+08:00","level":"INFO","msg":"handler: started","stream_id":"y6ghgxtk"}
{"time":"2025-07-28T22:29:42.608180773+08:00","level":"INFO","msg":"sender: started","stream_id":"y6ghgxtk"}
{"time":"2025-07-28T22:29:42.617560197+08:00","level":"INFO","msg":"Starting system monitor"}
