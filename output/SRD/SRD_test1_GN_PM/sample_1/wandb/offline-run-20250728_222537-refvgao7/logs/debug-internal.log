{"time":"2025-07-28T22:25:37.703831641+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test1_GN_PM/sample_1/wandb/offline-run-20250728_222537-refvgao7/logs/debug-core.log"}
{"time":"2025-07-28T22:25:37.816840375+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:25:37.817014982+08:00","level":"INFO","msg":"stream: created new stream","id":"refvgao7"}
{"time":"2025-07-28T22:25:37.817043601+08:00","level":"INFO","msg":"stream: started","id":"refvgao7"}
{"time":"2025-07-28T22:25:37.817073837+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"refvgao7"}
{"time":"2025-07-28T22:25:37.817128651+08:00","level":"INFO","msg":"sender: started","stream_id":"refvgao7"}
{"time":"2025-07-28T22:25:37.817189894+08:00","level":"INFO","msg":"handler: started","stream_id":"refvgao7"}
{"time":"2025-07-28T22:25:37.822777549+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:28:42.786171488+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:28:42.786333245+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:28:42.78749371+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T22:28:42.792188909+08:00","level":"INFO","msg":"stream: closing","id":"refvgao7"}
{"time":"2025-07-28T22:28:42.792208519+08:00","level":"INFO","msg":"handler: closed","stream_id":"refvgao7"}
{"time":"2025-07-28T22:28:42.792219109+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"refvgao7"}
{"time":"2025-07-28T22:28:42.792299417+08:00","level":"INFO","msg":"sender: closed","stream_id":"refvgao7"}
{"time":"2025-07-28T22:28:42.792354037+08:00","level":"INFO","msg":"stream: closed","id":"refvgao7"}
