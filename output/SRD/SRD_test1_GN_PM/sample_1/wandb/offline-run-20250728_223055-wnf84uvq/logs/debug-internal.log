{"time":"2025-07-28T22:30:55.666434642+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test1_GN_PM/sample_1/wandb/offline-run-20250728_223055-wnf84uvq/logs/debug-core.log"}
{"time":"2025-07-28T22:30:55.77804988+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:30:55.778249596+08:00","level":"INFO","msg":"stream: created new stream","id":"wnf84uvq"}
{"time":"2025-07-28T22:30:55.778281632+08:00","level":"INFO","msg":"stream: started","id":"wnf84uvq"}
{"time":"2025-07-28T22:30:55.778370318+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wnf84uvq"}
{"time":"2025-07-28T22:30:55.778449665+08:00","level":"INFO","msg":"sender: started","stream_id":"wnf84uvq"}
{"time":"2025-07-28T22:30:55.778559412+08:00","level":"INFO","msg":"handler: started","stream_id":"wnf84uvq"}
{"time":"2025-07-28T22:30:55.785139703+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:32:10.870277859+08:00","level":"INFO","msg":"stream: closing","id":"wnf84uvq"}
{"time":"2025-07-28T22:32:10.870377389+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:32:10.87047316+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:32:10.870824662+08:00","level":"INFO","msg":"handler: closed","stream_id":"wnf84uvq"}
{"time":"2025-07-28T22:32:10.870843542+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"wnf84uvq"}
{"time":"2025-07-28T22:32:10.870855018+08:00","level":"INFO","msg":"sender: closed","stream_id":"wnf84uvq"}
{"time":"2025-07-28T22:32:10.870998416+08:00","level":"INFO","msg":"stream: closed","id":"wnf84uvq"}
