{"time":"2025-07-28T15:25:43.248370686+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test15_KS5/sample_1/wandb/offline-run-20250728_152543-kn1g2srx/logs/debug-core.log"}
{"time":"2025-07-28T15:25:43.361250266+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T15:25:43.361441961+08:00","level":"INFO","msg":"stream: created new stream","id":"kn1g2srx"}
{"time":"2025-07-28T15:25:43.361469186+08:00","level":"INFO","msg":"stream: started","id":"kn1g2srx"}
{"time":"2025-07-28T15:25:43.361488985+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"kn1g2srx"}
{"time":"2025-07-28T15:25:43.361506803+08:00","level":"INFO","msg":"handler: started","stream_id":"kn1g2srx"}
{"time":"2025-07-28T15:25:43.361552643+08:00","level":"INFO","msg":"sender: started","stream_id":"kn1g2srx"}
{"time":"2025-07-28T15:25:43.366273788+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T15:30:08.874944324+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T15:30:08.875121157+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T15:30:08.876423396+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T15:30:08.880152222+08:00","level":"INFO","msg":"stream: closing","id":"kn1g2srx"}
{"time":"2025-07-28T15:30:08.880168711+08:00","level":"INFO","msg":"handler: closed","stream_id":"kn1g2srx"}
{"time":"2025-07-28T15:30:08.880177674+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"kn1g2srx"}
{"time":"2025-07-28T15:30:08.880194198+08:00","level":"INFO","msg":"sender: closed","stream_id":"kn1g2srx"}
{"time":"2025-07-28T15:30:08.880232796+08:00","level":"INFO","msg":"stream: closed","id":"kn1g2srx"}
