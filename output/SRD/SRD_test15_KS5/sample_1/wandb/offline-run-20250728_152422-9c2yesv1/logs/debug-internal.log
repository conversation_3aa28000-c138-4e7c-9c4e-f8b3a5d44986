{"time":"2025-07-28T15:24:22.389934102+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test15_KS5/sample_1/wandb/offline-run-20250728_152422-9c2yesv1/logs/debug-core.log"}
{"time":"2025-07-28T15:24:22.501648631+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T15:24:22.501826786+08:00","level":"INFO","msg":"stream: created new stream","id":"9c2yesv1"}
{"time":"2025-07-28T15:24:22.501854633+08:00","level":"INFO","msg":"stream: started","id":"9c2yesv1"}
{"time":"2025-07-28T15:24:22.501903431+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"9c2yesv1"}
{"time":"2025-07-28T15:24:22.501958132+08:00","level":"INFO","msg":"handler: started","stream_id":"9c2yesv1"}
{"time":"2025-07-28T15:24:22.502007839+08:00","level":"INFO","msg":"sender: started","stream_id":"9c2yesv1"}
{"time":"2025-07-28T15:24:22.507274048+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T15:25:25.591642595+08:00","level":"INFO","msg":"stream: closing","id":"9c2yesv1"}
{"time":"2025-07-28T15:25:25.591697136+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T15:25:25.591734841+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T15:25:25.591868046+08:00","level":"INFO","msg":"handler: closed","stream_id":"9c2yesv1"}
{"time":"2025-07-28T15:25:25.591895872+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"9c2yesv1"}
{"time":"2025-07-28T15:25:25.591950205+08:00","level":"INFO","msg":"sender: closed","stream_id":"9c2yesv1"}
{"time":"2025-07-28T15:25:25.592000625+08:00","level":"INFO","msg":"stream: closed","id":"9c2yesv1"}
