2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_setup.py:_flush():81] Configure stats pid to 12195
2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_setup.py:_flush():81] Loading settings from /root/.config/wandb/settings
2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_setup.py:_flush():81] Loading settings from /root/exp/yb/SRD/wandb/settings
2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_init.py:setup_run_log_directory():703] Logging user logs to output/SRD/SRD_test15_KS5/sample_1/wandb/offline-run-20250728_152422-9c2yesv1/logs/debug.log
2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to output/SRD/SRD_test15_KS5/sample_1/wandb/offline-run-20250728_152422-9c2yesv1/logs/debug-internal.log
2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_init.py:init():831] calling init triggers
2025-07-28 15:24:22,169 INFO    MainThread:12195 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'data': {'dataset': <class 'dataloader.datasets.Dataset_random_patch'>, 'config': <exps.configs.datasets.SRD.Config object at 0x7f01d0dd8d40>}, 'model': {'net': <class 'models.SRD.SRD.SRD'>, 'denoiser_in_ch': 3, 'out_ch': 1, 'sr_num': 1, 'base_ch': 4, 'dt_rank': 32, 'd_state': 32, 'depth': 2, 'vim_block_num': 2, 'dropout': 0.1, 'wavename': 'haar'}, 'dataloader': {'train': {'batch_size': 1, 'num_workers': 1}, 'val': {'batch_size': 1, 'num_workers': 1}, 'test': {'batch_size': 1, 'num_workers': 1}}, 'exp': {'project_name': 'SRD', 'exp_name': 'SRD_test15_KS5', 'gpu': '1', 'path_to_init': [PosixPath('output/SRD/SRD_test15_KS5/sample_1')], 'script': 'exps.scripts.SRD', 'script_func': {'train_denoiser': 'train', 'infer_denoiser': 'test', 'train_SR': 'train', 'infer_SR_one_double': 'test'}, 'train_dataset': 15, 'test_dataset': 15, 'save_path': PosixPath('output/SRD/SRD_test15_KS5/sample_1'), 'split_size': 32, 'compute_metrics': True, 'debug': False, 'denoise_iters': 50, 'sr_iters': 50, 'val_freq': 10, 'infer_denoiser_name': 'minLoss', 'infer_SR_name': 'final', 'noise_factor': [0.1, 1, 1]}, 'optim': {'lr': 0.001, 'weight_decay': 0.0001}, '_wandb': {}}
2025-07-28 15:24:22,170 INFO    MainThread:12195 [wandb_init.py:init():872] starting backend
2025-07-28 15:24:22,378 INFO    MainThread:12195 [wandb_init.py:init():875] sending inform_init request
2025-07-28 15:24:22,384 INFO    MainThread:12195 [wandb_init.py:init():883] backend started and connected
2025-07-28 15:24:22,386 INFO    MainThread:12195 [wandb_init.py:init():956] updated telemetry
2025-07-28 15:24:22,386 INFO    MainThread:12195 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-07-28 15:24:22,504 INFO    MainThread:12195 [wandb_init.py:init():1032] starting run threads in backend
2025-07-28 15:24:22,625 INFO    MainThread:12195 [wandb_run.py:_console_start():2453] atexit reg
2025-07-28 15:24:22,626 INFO    MainThread:12195 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-07-28 15:24:22,626 INFO    MainThread:12195 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-07-28 15:24:22,626 INFO    MainThread:12195 [wandb_run.py:_redirect():2393] Redirects installed.
2025-07-28 15:24:22,627 INFO    MainThread:12195 [wandb_init.py:init():1078] run started, returning control to user process
2025-07-28 15:25:25,590 INFO    MsgRouterThr:12195 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 0 handles.
