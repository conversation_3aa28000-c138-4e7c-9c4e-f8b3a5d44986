{"time":"2025-07-28T15:30:10.291429361+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test15_KS5/sample_1/wandb/offline-run-20250728_153010-b00ag0p0/logs/debug-core.log"}
{"time":"2025-07-28T15:30:10.401209606+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T15:30:10.401374476+08:00","level":"INFO","msg":"stream: created new stream","id":"b00ag0p0"}
{"time":"2025-07-28T15:30:10.401400864+08:00","level":"INFO","msg":"stream: started","id":"b00ag0p0"}
{"time":"2025-07-28T15:30:10.401486245+08:00","level":"INFO","msg":"handler: started","stream_id":"b00ag0p0"}
{"time":"2025-07-28T15:30:10.401481495+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"b00ag0p0"}
{"time":"2025-07-28T15:30:10.401509911+08:00","level":"INFO","msg":"sender: started","stream_id":"b00ag0p0"}
{"time":"2025-07-28T15:30:10.40678657+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T15:37:05.81137456+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T15:37:05.811484507+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T15:37:05.812461689+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T15:37:05.815391919+08:00","level":"INFO","msg":"stream: closing","id":"b00ag0p0"}
{"time":"2025-07-28T15:37:05.815412039+08:00","level":"INFO","msg":"handler: closed","stream_id":"b00ag0p0"}
{"time":"2025-07-28T15:37:05.815427266+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"b00ag0p0"}
{"time":"2025-07-28T15:37:05.815463443+08:00","level":"INFO","msg":"sender: closed","stream_id":"b00ag0p0"}
{"time":"2025-07-28T15:37:05.815563663+08:00","level":"INFO","msg":"stream: closed","id":"b00ag0p0"}
