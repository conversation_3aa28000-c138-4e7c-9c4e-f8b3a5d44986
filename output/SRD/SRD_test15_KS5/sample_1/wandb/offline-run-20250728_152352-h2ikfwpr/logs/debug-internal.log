{"time":"2025-07-28T15:23:52.825087324+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test15_KS5/sample_1/wandb/offline-run-20250728_152352-h2ikfwpr/logs/debug-core.log"}
{"time":"2025-07-28T15:23:52.936131205+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T15:23:52.93628198+08:00","level":"INFO","msg":"stream: created new stream","id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:52.936303556+08:00","level":"INFO","msg":"stream: started","id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:52.936402994+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:52.936491197+08:00","level":"INFO","msg":"sender: started","stream_id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:52.936560321+08:00","level":"INFO","msg":"handler: started","stream_id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:52.943124479+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T15:23:58.951308914+08:00","level":"INFO","msg":"stream: closing","id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:58.951372825+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T15:23:58.951422914+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T15:23:58.951545595+08:00","level":"INFO","msg":"handler: closed","stream_id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:58.951558755+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:58.95157023+08:00","level":"INFO","msg":"sender: closed","stream_id":"h2ikfwpr"}
{"time":"2025-07-28T15:23:58.951634191+08:00","level":"INFO","msg":"stream: closed","id":"h2ikfwpr"}
