{"time":"2025-07-28T15:28:01.470629253+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test17_KS5/sample_1/wandb/offline-run-20250728_152801-znkb27p7/logs/debug-core.log"}
{"time":"2025-07-28T15:28:01.580584049+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T15:28:01.580747634+08:00","level":"INFO","msg":"stream: created new stream","id":"znkb27p7"}
{"time":"2025-07-28T15:28:01.580772516+08:00","level":"INFO","msg":"stream: started","id":"znkb27p7"}
{"time":"2025-07-28T15:28:01.580828982+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"znkb27p7"}
{"time":"2025-07-28T15:28:01.580847029+08:00","level":"INFO","msg":"sender: started","stream_id":"znkb27p7"}
{"time":"2025-07-28T15:28:01.580879039+08:00","level":"INFO","msg":"handler: started","stream_id":"znkb27p7"}
{"time":"2025-07-28T15:28:01.585762848+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T15:33:04.21746709+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T15:33:04.217575138+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T15:33:04.218623314+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T15:33:04.223125275+08:00","level":"INFO","msg":"stream: closing","id":"znkb27p7"}
{"time":"2025-07-28T15:33:04.223149811+08:00","level":"INFO","msg":"handler: closed","stream_id":"znkb27p7"}
{"time":"2025-07-28T15:33:04.223163978+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"znkb27p7"}
{"time":"2025-07-28T15:33:04.223196207+08:00","level":"INFO","msg":"sender: closed","stream_id":"znkb27p7"}
{"time":"2025-07-28T15:33:04.223317503+08:00","level":"INFO","msg":"stream: closed","id":"znkb27p7"}
