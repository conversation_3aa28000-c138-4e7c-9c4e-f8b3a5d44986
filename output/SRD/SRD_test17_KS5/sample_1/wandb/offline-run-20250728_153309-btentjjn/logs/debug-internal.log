{"time":"2025-07-28T15:33:09.615296433+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test17_KS5/sample_1/wandb/offline-run-20250728_153309-btentjjn/logs/debug-core.log"}
{"time":"2025-07-28T15:33:09.723579705+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T15:33:09.723773318+08:00","level":"INFO","msg":"stream: created new stream","id":"btentjjn"}
{"time":"2025-07-28T15:33:09.723854998+08:00","level":"INFO","msg":"stream: started","id":"btentjjn"}
{"time":"2025-07-28T15:33:09.723893653+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"btentjjn"}
{"time":"2025-07-28T15:33:09.723918876+08:00","level":"INFO","msg":"sender: started","stream_id":"btentjjn"}
{"time":"2025-07-28T15:33:09.723979935+08:00","level":"INFO","msg":"handler: started","stream_id":"btentjjn"}
{"time":"2025-07-28T15:33:09.745655286+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T15:41:01.417899303+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T15:41:01.417978556+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T15:41:01.418843943+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T15:41:01.421437619+08:00","level":"INFO","msg":"stream: closing","id":"btentjjn"}
{"time":"2025-07-28T15:41:01.421462504+08:00","level":"INFO","msg":"handler: closed","stream_id":"btentjjn"}
{"time":"2025-07-28T15:41:01.421476924+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"btentjjn"}
{"time":"2025-07-28T15:41:01.421546015+08:00","level":"INFO","msg":"sender: closed","stream_id":"btentjjn"}
{"time":"2025-07-28T15:41:01.421621827+08:00","level":"INFO","msg":"stream: closed","id":"btentjjn"}
