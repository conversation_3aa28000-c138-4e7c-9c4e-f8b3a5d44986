{"time":"2025-08-03T14:50:57.960756382+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T14:50:58.128939364+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T14:50:58.129125557+08:00","level":"INFO","msg":"stream: created new stream","id":"eze0weow"}
{"time":"2025-08-03T14:50:58.129158626+08:00","level":"INFO","msg":"stream: started","id":"eze0weow"}
{"time":"2025-08-03T14:50:58.129299823+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"eze0weow"}
{"time":"2025-08-03T14:50:58.12936172+08:00","level":"INFO","msg":"handler: started","stream_id":"eze0weow"}
{"time":"2025-08-03T14:50:58.129417081+08:00","level":"INFO","msg":"sender: started","stream_id":"eze0weow"}
{"time":"2025-08-03T14:50:58.130709873+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T14:55:19.239170281+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T14:55:19.242814453+08:00","level":"INFO","msg":"stream: closing","id":"eze0weow"}
{"time":"2025-08-03T14:55:19.242826606+08:00","level":"INFO","msg":"handler: closed","stream_id":"eze0weow"}
{"time":"2025-08-03T14:55:19.24283431+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"eze0weow"}
{"time":"2025-08-03T14:55:19.242853268+08:00","level":"INFO","msg":"sender: closed","stream_id":"eze0weow"}
{"time":"2025-08-03T14:55:19.242907131+08:00","level":"INFO","msg":"stream: closed","id":"eze0weow"}
