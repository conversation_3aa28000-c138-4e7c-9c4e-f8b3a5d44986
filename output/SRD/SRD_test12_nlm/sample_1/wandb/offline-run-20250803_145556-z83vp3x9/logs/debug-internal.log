{"time":"2025-08-03T14:55:56.283380629+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-03T14:55:56.43942172+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-03T14:55:56.439546138+08:00","level":"INFO","msg":"stream: created new stream","id":"z83vp3x9"}
{"time":"2025-08-03T14:55:56.439597005+08:00","level":"INFO","msg":"stream: started","id":"z83vp3x9"}
{"time":"2025-08-03T14:55:56.439681371+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"z83vp3x9"}
{"time":"2025-08-03T14:55:56.439774602+08:00","level":"INFO","msg":"handler: started","stream_id":"z83vp3x9"}
{"time":"2025-08-03T14:55:56.439829872+08:00","level":"INFO","msg":"sender: started","stream_id":"z83vp3x9"}
{"time":"2025-08-03T14:55:56.440232739+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-03T15:01:44.058634508+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-08-03T15:01:44.061517005+08:00","level":"INFO","msg":"stream: closing","id":"z83vp3x9"}
{"time":"2025-08-03T15:01:44.061534984+08:00","level":"INFO","msg":"handler: closed","stream_id":"z83vp3x9"}
{"time":"2025-08-03T15:01:44.061543995+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"z83vp3x9"}
{"time":"2025-08-03T15:01:44.061570683+08:00","level":"INFO","msg":"sender: closed","stream_id":"z83vp3x9"}
{"time":"2025-08-03T15:01:44.061621076+08:00","level":"INFO","msg":"stream: closed","id":"z83vp3x9"}
