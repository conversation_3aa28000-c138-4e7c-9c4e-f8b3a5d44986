addict==2.4.0
wheel==0.45.1
pip==25.1
click==8.1.8
MarkupSafe==3.0.2
Brotli==1.0.9
cycler==0.11.0
kiwisolver==1.4.8
mkl-service==2.4.0
packaging==25.0
pyparsing==3.2.0
tzdata==2025.2
pytz==2025.2
setuptools==72.1.0
six==1.17.0
fonttools==4.55.3
einops==0.8.1
pillow==11.3.0
python-dateutil==2.9.0.post0
Bottleneck==1.4.2
contourpy==1.3.1
matplotlib==3.10.0
mkl_fft==1.3.11
mkl_random==1.2.8
numexpr==2.11.0
pandas==2.3.1
seaborn==0.13.2
numpy==2.2.6
opencv-python==*********
tqdm==4.67.1
joblib==1.4.2
threadpoolctl==3.5.0
scipy==1.16.0
scikit-learn==1.7.1
networkx==3.4.2
lazy_loader==0.4
imageio==2.37.0
tifffile==2025.2.18
scikit-image==0.25.2
wcwidth==0.2.13
nvidia-ml-py==12.575.51
psutil==7.0.0
blessed==1.21.0
gpustat==1.1.1
nvidia-cusparselt-cu12==0.6.3
mpmath==1.3.0
typing_extensions==4.14.1
triton==3.3.1
sympy==1.14.0
nvidia-nvtx-cu12==12.6.77
nvidia-nvjitlink-cu12==12.6.85
nvidia-nccl-cu12==2.26.2
nvidia-curand-cu12==*********
nvidia-cufile-cu12==********
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cublas-cu12==********
fsspec==2025.7.0
filelock==3.18.0
nvidia-cusparse-cu12==********
nvidia-cufft-cu12==********
nvidia-cudnn-cu12==********
Jinja2==3.1.6
nvidia-cusolver-cu12==********
torch==2.7.1
pytorch-wavelets==1.3.0
PyWavelets==1.8.0
torchvision==0.22.1+cu128
torchaudio==2.7.1+cu128
nlm-torch-new==0.1.3
wrapt==1.17.2
urllib3==2.5.0
typing-inspection==0.4.1
simplejson==3.20.1
PyYAML==6.0.2
pynvml==12.0.0
Pygments==2.19.2
pydantic_core==2.33.2
protobuf==6.31.1
prettytable==3.16.0
platformdirs==4.3.8
mdurl==0.1.2
jmespath==1.0.1
idna==3.10
charset-normalizer==3.4.2
certifi==2025.8.3
annotated-types==0.7.0
swankit==0.2.4
requests==2.32.4
pyecharts==2.0.8
pydantic==2.11.7
markdown-it-py==3.0.0
botocore==1.40.1
s3transfer==0.13.1
rich==13.9.4
boto3==1.40.1
smmap==5.0.2
sentry-sdk==2.34.1
gitdb==4.0.12
GitPython==3.1.45
wandb==0.21.0
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
platformdirs==4.2.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
zipp==3.19.2
importlib_resources==6.4.0
ordered-set==4.1.0
packaging==24.1
wheel==0.43.0
