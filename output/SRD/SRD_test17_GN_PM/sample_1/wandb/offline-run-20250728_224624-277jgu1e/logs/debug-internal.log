{"time":"2025-07-28T22:46:24.889403241+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test17_GN_PM/sample_1/wandb/offline-run-20250728_224624-277jgu1e/logs/debug-core.log"}
{"time":"2025-07-28T22:46:25.000530511+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:46:25.000668163+08:00","level":"INFO","msg":"stream: created new stream","id":"277jgu1e"}
{"time":"2025-07-28T22:46:25.000693251+08:00","level":"INFO","msg":"stream: started","id":"277jgu1e"}
{"time":"2025-07-28T22:46:25.000774762+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"277jgu1e"}
{"time":"2025-07-28T22:46:25.000859272+08:00","level":"INFO","msg":"sender: started","stream_id":"277jgu1e"}
{"time":"2025-07-28T22:46:25.000939503+08:00","level":"INFO","msg":"handler: started","stream_id":"277jgu1e"}
{"time":"2025-07-28T22:46:25.007016143+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:50:52.029250151+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:50:52.029357587+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:50:52.030486866+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T22:50:52.034832659+08:00","level":"INFO","msg":"stream: closing","id":"277jgu1e"}
{"time":"2025-07-28T22:50:52.034861554+08:00","level":"INFO","msg":"handler: closed","stream_id":"277jgu1e"}
{"time":"2025-07-28T22:50:52.034876274+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"277jgu1e"}
{"time":"2025-07-28T22:50:52.0349066+08:00","level":"INFO","msg":"sender: closed","stream_id":"277jgu1e"}
{"time":"2025-07-28T22:50:52.034972163+08:00","level":"INFO","msg":"stream: closed","id":"277jgu1e"}
