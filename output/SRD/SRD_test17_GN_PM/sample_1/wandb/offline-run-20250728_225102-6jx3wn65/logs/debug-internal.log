{"time":"2025-07-28T22:51:02.343848472+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test17_GN_PM/sample_1/wandb/offline-run-20250728_225102-6jx3wn65/logs/debug-core.log"}
{"time":"2025-07-28T22:51:02.454356409+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:51:02.45452132+08:00","level":"INFO","msg":"stream: created new stream","id":"6jx3wn65"}
{"time":"2025-07-28T22:51:02.454547983+08:00","level":"INFO","msg":"stream: started","id":"6jx3wn65"}
{"time":"2025-07-28T22:51:02.454644821+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"6jx3wn65"}
{"time":"2025-07-28T22:51:02.454717508+08:00","level":"INFO","msg":"sender: started","stream_id":"6jx3wn65"}
{"time":"2025-07-28T22:51:02.454768441+08:00","level":"INFO","msg":"handler: started","stream_id":"6jx3wn65"}
{"time":"2025-07-28T22:51:02.460120455+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:58:35.476927565+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:58:35.477059749+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:58:35.47811295+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T22:58:35.481693711+08:00","level":"INFO","msg":"stream: closing","id":"6jx3wn65"}
{"time":"2025-07-28T22:58:35.481720258+08:00","level":"INFO","msg":"handler: closed","stream_id":"6jx3wn65"}
{"time":"2025-07-28T22:58:35.481735385+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"6jx3wn65"}
{"time":"2025-07-28T22:58:35.481798707+08:00","level":"INFO","msg":"sender: closed","stream_id":"6jx3wn65"}
{"time":"2025-07-28T22:58:35.481875187+08:00","level":"INFO","msg":"stream: closed","id":"6jx3wn65"}
