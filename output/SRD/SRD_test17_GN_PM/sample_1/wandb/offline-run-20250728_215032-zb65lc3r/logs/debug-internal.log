{"time":"2025-07-28T21:50:32.008731263+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test17_GN_PM/sample_1/wandb/offline-run-20250728_215032-zb65lc3r/logs/debug-core.log"}
{"time":"2025-07-28T21:50:32.120312742+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T21:50:32.120505403+08:00","level":"INFO","msg":"stream: created new stream","id":"zb65lc3r"}
{"time":"2025-07-28T21:50:32.1205374+08:00","level":"INFO","msg":"stream: started","id":"zb65lc3r"}
{"time":"2025-07-28T21:50:32.120639287+08:00","level":"INFO","msg":"handler: started","stream_id":"zb65lc3r"}
{"time":"2025-07-28T21:50:32.120636296+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"zb65lc3r"}
{"time":"2025-07-28T21:50:32.120719511+08:00","level":"INFO","msg":"sender: started","stream_id":"zb65lc3r"}
{"time":"2025-07-28T21:50:32.126122826+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T21:58:49.063226264+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T21:58:49.063318286+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T21:58:49.0642936+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T21:58:49.067502758+08:00","level":"INFO","msg":"stream: closing","id":"zb65lc3r"}
{"time":"2025-07-28T21:58:49.067521154+08:00","level":"INFO","msg":"handler: closed","stream_id":"zb65lc3r"}
{"time":"2025-07-28T21:58:49.067532885+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"zb65lc3r"}
{"time":"2025-07-28T21:58:49.067542154+08:00","level":"INFO","msg":"sender: closed","stream_id":"zb65lc3r"}
{"time":"2025-07-28T21:58:49.067632547+08:00","level":"INFO","msg":"stream: closed","id":"zb65lc3r"}
