{"time":"2025-07-28T22:03:36.78530868+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test19_GN_PM/sample_1/wandb/offline-run-20250728_220336-kri4kzow/logs/debug-core.log"}
{"time":"2025-07-28T22:03:36.897149085+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T22:03:36.897312575+08:00","level":"INFO","msg":"stream: created new stream","id":"kri4kzow"}
{"time":"2025-07-28T22:03:36.897359605+08:00","level":"INFO","msg":"stream: started","id":"kri4kzow"}
{"time":"2025-07-28T22:03:36.897449684+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"kri4kzow"}
{"time":"2025-07-28T22:03:36.897531181+08:00","level":"INFO","msg":"sender: started","stream_id":"kri4kzow"}
{"time":"2025-07-28T22:03:36.8976615+08:00","level":"INFO","msg":"handler: started","stream_id":"kri4kzow"}
{"time":"2025-07-28T22:03:36.904567866+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T22:05:17.016160239+08:00","level":"INFO","msg":"stream: closing","id":"kri4kzow"}
{"time":"2025-07-28T22:05:17.016239763+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T22:05:17.016337476+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T22:05:17.016572185+08:00","level":"INFO","msg":"handler: closed","stream_id":"kri4kzow"}
{"time":"2025-07-28T22:05:17.016590752+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"kri4kzow"}
{"time":"2025-07-28T22:05:17.016631507+08:00","level":"INFO","msg":"sender: closed","stream_id":"kri4kzow"}
{"time":"2025-07-28T22:05:17.016712889+08:00","level":"INFO","msg":"stream: closed","id":"kri4kzow"}
