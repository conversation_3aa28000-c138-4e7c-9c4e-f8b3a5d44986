{"time":"2025-07-28T21:52:34.518639859+08:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"output/SRD/SRD_test19_GN_PM/sample_1/wandb/offline-run-20250728_215234-5oy2flcd/logs/debug-core.log"}
{"time":"2025-07-28T21:52:34.629736079+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-28T21:52:34.629865703+08:00","level":"INFO","msg":"stream: created new stream","id":"5oy2flcd"}
{"time":"2025-07-28T21:52:34.62988646+08:00","level":"INFO","msg":"stream: started","id":"5oy2flcd"}
{"time":"2025-07-28T21:52:34.629973046+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"5oy2flcd"}
{"time":"2025-07-28T21:52:34.629987071+08:00","level":"INFO","msg":"sender: started","stream_id":"5oy2flcd"}
{"time":"2025-07-28T21:52:34.630035024+08:00","level":"INFO","msg":"handler: started","stream_id":"5oy2flcd"}
{"time":"2025-07-28T21:52:34.635641979+08:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-07-28T21:57:19.779196059+08:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-07-28T21:57:19.779316123+08:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-07-28T21:57:19.780457416+08:00","level":"INFO","msg":"handler: operation stats","stats":{}}
{"time":"2025-07-28T21:57:19.785198996+08:00","level":"INFO","msg":"stream: closing","id":"5oy2flcd"}
{"time":"2025-07-28T21:57:19.785242874+08:00","level":"INFO","msg":"handler: closed","stream_id":"5oy2flcd"}
{"time":"2025-07-28T21:57:19.785257418+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"5oy2flcd"}
{"time":"2025-07-28T21:57:19.785269811+08:00","level":"INFO","msg":"sender: closed","stream_id":"5oy2flcd"}
{"time":"2025-07-28T21:57:19.785346233+08:00","level":"INFO","msg":"stream: closed","id":"5oy2flcd"}
